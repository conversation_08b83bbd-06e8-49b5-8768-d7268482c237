# Simulation Mode Guide

## Overview

The price support strategy now includes a comprehensive **simulation mode** that allows you to test the entire strategy logic without risking real funds or placing actual orders. This is perfect for testing, debugging, and understanding how the strategy works before going live.

## ✅ **Environment Variables Configuration**

### **Required Environment Variables**

```bash
# Enable/disable simulation mode
SIMULATION=true                           # Set to "false" for live trading

# Simulation balance parameters
SIMULATION_DEX_WETH_BALANCE=1.0          # WETH balance on DEX (e.g., 1.0 WETH)
SIMULATION_MEXC_USDT_BALANCE=5000.0      # USDT balance on MEXC (e.g., $5000)
SIMULATION_MEXC_FUAL_BALANCE=1000000.0   # FUAL balance on MEXC (e.g., 1M FUAL)
SIMULATION_DEX_FUAL_BALANCE=500000.0     # FUAL balance on DEX (e.g., 500K FUAL)
```

### **Example Configuration**

**For Testing with Moderate Balances:**
```bash
export SIMULATION=true
export SIMULATION_DEX_WETH_BALANCE=0.5
export SIMULATION_MEXC_USDT_BALANCE=2000.0
export SIMULATION_MEXC_FUAL_BALANCE=500000.0
export SIMULATION_DEX_FUAL_BALANCE=250000.0
```

**For Testing with Large Balances:**
```bash
export SIMULATION=true
export SIMULATION_DEX_WETH_BALANCE=5.0
export SIMULATION_MEXC_USDT_BALANCE=20000.0
export SIMULATION_MEXC_FUAL_BALANCE=5000000.0
export SIMULATION_DEX_FUAL_BALANCE=2500000.0
```

**For Live Trading:**
```bash
export SIMULATION=false
# No simulation balance parameters needed
```

## **How Simulation Mode Works**

### **1. Automatic Detection**
```python
# Strategy automatically detects simulation mode on startup
if SIMULATION=true:
    🎮 SIMULATION MODE ENABLED
    - All calculations will be performed
    - Orders will be simulated (not placed)
    - Balances will use simulation parameters
else:
    💰 LIVE TRADING MODE
    - Real balances will be used
    - Orders will be placed on exchanges
```

### **2. Simulated Balance Management**
```python
# Uses simulation balances instead of real balances
def _get_balance(asset, exchange):
    if simulation_mode:
        return simulation_balances[asset][exchange]
    else:
        return real_exchange_balance(asset)
```

### **3. Simulated Order Placement**
```python
# Orders are logged but not actually placed
🎮 SIMULATED ORDER: SIM_000001
- Type: BUY
- Price: $0.001234
- Size: 50000.000000 FUAL
- Value: $61.70
- Reason: Support layer 1 below current price
```

### **4. Simulated DEX Transactions**
```python
# DEX swaps are simulated with balance updates
🎮 SIMULATED DEX TRANSACTION: SIM_TX_000001
- Type: UNISWAP_SWAP
- Amount: 0.123456 WETH
- input_token: WETH
- output_token: FUAL
- usd_value: $400.00
```

## **Simulation Features**

### **✅ Complete Strategy Logic**
- **All calculations performed**: Market analysis, arbitrage detection, parameter tuning
- **Real-time price data**: Uses actual ETH/USDT prices and market data
- **Adaptive parameters**: Parameter tuning system works in simulation
- **Coordinated trading**: Both MEXC and DEX operations are simulated

### **✅ Balance Tracking**
```
🎮 SIMULATED BALANCE UPDATE:
- USDT spent: $123.45
- USDT balance: $5000.00 → $4876.55
- FUAL received: 123450.00
- FUAL balance: 1000000.00 → 1123450.00
```

### **✅ Order Management**
- **Order IDs**: Simulated orders get unique IDs (SIM_000001, SIM_000002, etc.)
- **Order tracking**: All simulated orders are tracked in order history
- **Performance metrics**: Success rates and performance tracking work normally

### **✅ DEX Integration**
- **Wallet balance checks**: Uses simulated WETH/FUAL balances
- **Swap simulations**: Estimates token amounts received
- **Transaction logging**: All DEX operations are logged with details

## **Simulation Output Examples**

### **Strategy Startup**
```
PRICE_SUPPORT: 🎮 SIMULATION MODE ENABLED
PRICE_SUPPORT: - All calculations will be performed
PRICE_SUPPORT: - Orders will be simulated (not placed)
PRICE_SUPPORT: - Balances will use simulation parameters

PRICE_SUPPORT: 📊 Simulation balances loaded:
PRICE_SUPPORT: - DEX WETH: 1.000000
PRICE_SUPPORT: - MEXC USDT: $5000.00
PRICE_SUPPORT: - MEXC FUAL: 1000000.00
PRICE_SUPPORT: - DEX FUAL: 500000.00
```

### **Simulated Order Execution**
```
PRICE_SUPPORT: 🎮 EXECUTING SIMULATED ORDER PROPOSAL

PRICE_SUPPORT: 🎮 SIMULATED ORDER: SIM_000001
PRICE_SUPPORT: - Type: BUY
PRICE_SUPPORT: - Price: $0.001234
PRICE_SUPPORT: - Size: 50000.000000 FUAL
PRICE_SUPPORT: - Value: $61.70
PRICE_SUPPORT: - Reason: Support layer 1 below current price

PRICE_SUPPORT: 🎮 SIMULATED BALANCE UPDATE:
PRICE_SUPPORT: - USDT spent: $61.70
PRICE_SUPPORT: - USDT balance: $5000.00 → $4938.30
PRICE_SUPPORT: - FUAL received: 50000.00
PRICE_SUPPORT: - FUAL balance: 1000000.00 → 1050000.00

PRICE_SUPPORT: 🎮 SIMULATION COMPLETE: 1 orders simulated
```

### **Simulated DEX Swap**
```
PRICE_SUPPORT: 🎮 SIMULATED: Executing Uniswap swap for $400.00

PRICE_SUPPORT: 🎮 SIMULATED DEX TRANSACTION: SIM_TX_000001
PRICE_SUPPORT: - Type: UNISWAP_SWAP
PRICE_SUPPORT: - Amount: 0.123456
PRICE_SUPPORT: - input_token: WETH
PRICE_SUPPORT: - output_token: FUAL
PRICE_SUPPORT: - input_amount: 0.123456 WETH
PRICE_SUPPORT: - usd_value: $400.00
PRICE_SUPPORT: - eth_price: $3247.82
PRICE_SUPPORT: - slippage_tolerance: 2.40%

PRICE_SUPPORT: 🎮 SIMULATED BALANCE UPDATE:
PRICE_SUPPORT: - WETH spent: 0.123456
PRICE_SUPPORT: - WETH balance: 1.000000 → 0.876544
PRICE_SUPPORT: - FUAL received: 400000.00
PRICE_SUPPORT: - FUAL balance: 500000.00 → 900000.00

PRICE_SUPPORT: 🎮 ✅ Simulated Uniswap swap completed successfully
```

### **Coordinated Strategy Simulation**
```
PRICE_SUPPORT: 🤝 Coordinated strategy recommended
PRICE_SUPPORT: 📊 MEXC Orders: 3 orders, $185.40 total
PRICE_SUPPORT: 🔄 DEX Support: $400.00 Uniswap swap
PRICE_SUPPORT: 📈 Net Price Impact: +2.34%

PRICE_SUPPORT: 🎮 Executing coordinated simulation...
[MEXC orders simulated]
[DEX swap simulated]
PRICE_SUPPORT: ✅ Coordinated simulation completed
```

## **Benefits of Simulation Mode**

### **✅ Risk-Free Testing**
- **No real money**: Test strategy logic without financial risk
- **No real orders**: Orders are logged but not placed on exchanges
- **Safe experimentation**: Try different parameters and configurations

### **✅ Strategy Validation**
- **Logic verification**: Ensure all calculations work correctly
- **Parameter tuning**: Test adaptive parameter adjustments
- **Market response**: See how strategy responds to real market conditions

### **✅ Performance Analysis**
- **Order patterns**: Analyze order placement patterns
- **Balance management**: Track how balances change over time
- **Arbitrage detection**: Verify arbitrage calculations are accurate

### **✅ Development & Debugging**
- **Code testing**: Test new features safely
- **Bug identification**: Find issues before live trading
- **Performance optimization**: Optimize strategy parameters

## **Switching Between Modes**

### **To Enable Simulation Mode:**
```bash
export SIMULATION=true
export SIMULATION_DEX_WETH_BALANCE=1.0
export SIMULATION_MEXC_USDT_BALANCE=5000.0
export SIMULATION_MEXC_FUAL_BALANCE=1000000.0
export SIMULATION_DEX_FUAL_BALANCE=500000.0

# Restart Hummingbot
```

### **To Enable Live Trading:**
```bash
export SIMULATION=false
# Remove or comment out simulation balance variables

# Restart Hummingbot
```

### **Environment Variable Validation**
```python
# Strategy validates environment variables on startup
if SIMULATION=true:
    ✅ All simulation parameters loaded correctly
else:
    ✅ Live trading mode - using real balances
```

## **Simulation Summary API**

### **Get Simulation Status**
```python
summary = strategy.get_simulation_summary()

{
    "simulation_mode": true,
    "total_orders": 15,
    "total_value": 1234.56,
    "balances": {
        "dex_weth_balance": 0.876544,
        "mexc_usdt_balance": 3765.44,
        "mexc_fual_balance": 1234567.89,
        "dex_fual_balance": 900000.00
    },
    "orders": [...]  # Last 10 orders
}
```

## **Best Practices**

### **✅ Testing Workflow**
1. **Start with simulation**: Always test new configurations in simulation mode
2. **Monitor logs**: Watch simulation output to understand strategy behavior
3. **Validate calculations**: Ensure arbitrage and parameter calculations are correct
4. **Test edge cases**: Try different market conditions and balance scenarios
5. **Switch to live**: Only go live after thorough simulation testing

### **✅ Balance Configuration**
- **Realistic balances**: Use realistic balance amounts for accurate testing
- **Sufficient USDT**: Ensure enough USDT for meaningful order placement
- **Adequate WETH**: Provide sufficient WETH for DEX operations
- **Proportional amounts**: Keep balances proportional to your actual trading capital

### **✅ Monitoring**
- **Watch balance changes**: Monitor how balances change during simulation
- **Track order patterns**: Analyze order placement frequency and sizes
- **Verify arbitrage**: Ensure arbitrage detection works correctly
- **Check coordination**: Verify MEXC + DEX coordination logic

## **Troubleshooting**

### **Common Issues**
```bash
# Issue: Simulation not enabled
Solution: Check SIMULATION environment variable is set to "true"

# Issue: Invalid balance parameters
Solution: Ensure all simulation balance variables are valid decimal numbers

# Issue: Strategy using real balances
Solution: Restart Hummingbot after setting environment variables
```

### **Validation Checks**
```python
# Strategy performs validation on startup
✅ Simulation mode: true
✅ DEX WETH balance: 1.000000
✅ MEXC USDT balance: 5000.00
✅ MEXC FUAL balance: 1000000.00
✅ DEX FUAL balance: 500000.00
```

Simulation mode provides a complete, risk-free environment for testing and validating the price support strategy before deploying it with real funds.
