import asyncio
import json
from decimal import Decimal
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, Union

from bidict import bidict

from hummingbot.connector.constants import s_decimal_NaN
from hummingbot.connector.exchange.mexc import mexc_constants as CONSTANTS, mexc_utils, mexc_web_utils as web_utils
from hummingbot.connector.exchange.mexc.mexc_api_order_book_data_source import MexcAPIOrderBookDataSource
from hummingbot.connector.exchange.mexc.mexc_api_user_stream_data_source import MexcAPIUserStreamDataSource
from hummingbot.connector.exchange.mexc.mexc_auth import MexcAuth
from hummingbot.connector.exchange_py_base import ExchangePyBase
from hummingbot.connector.trading_rule import TradingRule
from hummingbot.connector.utils import TradeFillOrderDetails, combine_to_hb_trading_pair
from hummingbot.core.data_type.cancellation_result import CancellationResult
from hummingbot.core.data_type.common import OrderType, TradeType
from hummingbot.core.data_type.in_flight_order import In<PERSON><PERSON><PERSON>rde<PERSON>, OrderUpdate, TradeUpdate
from hummingbot.core.data_type.order_book_tracker_data_source import OrderBook<PERSON>rackerDataSource
from hummingbot.core.data_type.trade_fee import DeductedFromReturnsTradeFee, TokenAmount, TradeFeeBase
from hummingbot.core.data_type.user_stream_tracker_data_source import UserStreamTrackerDataSource
from hummingbot.core.event.events import MarketEvent, OrderFilledEvent
from hummingbot.core.utils.async_utils import safe_gather
from hummingbot.core.web_assistant.connections.data_types import RESTMethod
from hummingbot.core.web_assistant.web_assistants_factory import WebAssistantsFactory

if TYPE_CHECKING:
    from hummingbot.client.config.config_helpers import ClientConfigAdapter


class MexcExchange(ExchangePyBase):
    UPDATE_ORDER_STATUS_MIN_INTERVAL = 10.0

    web_utils = web_utils

    def __init__(self,
                 client_config_map: "ClientConfigAdapter",
                 mexc_api_key: str,
                 mexc_api_secret: str,
                 trading_pairs: Optional[List[str]] = None,
                 trading_required: bool = True,
                 domain: str = CONSTANTS.DEFAULT_DOMAIN,
                 ):
        self.api_key = mexc_api_key
        self.secret_key = mexc_api_secret
        self._domain = domain
        self._trading_required = trading_required
        self._trading_pairs = trading_pairs
        self._last_trades_poll_mexc_timestamp = 1.0

        # Rate limit handling for 429 errors
        self._rate_limit_backoff_time = 1.0
        self._rate_limit_backoff_multiplier = 2.0
        self._max_rate_limit_backoff = 600.0  # 10 minutes max
        self._min_rate_limit_backoff = 1.0    # 1 second min
        self._last_429_timestamp = 0.0
        self._consecutive_429_count = 0

        # Cancel order rate limiting (10 orders per second)
        self._cancel_order_queue = []
        self._last_cancel_batch_time = 0.0
        self._cancel_orders_per_second = 10
        self._cancel_batch_interval = 1.0  # 1 second
        self._orders_being_canceled = set()  # Track orders currently being canceled

        super().__init__(client_config_map)

    @staticmethod
    def mexc_order_type(order_type: OrderType) -> str:
        return order_type.name.upper()

    @staticmethod
    def to_hb_order_type(mexc_type: str) -> OrderType:
        return OrderType[mexc_type]

    @property
    def authenticator(self):
        return MexcAuth(
            api_key=self.api_key,
            secret_key=self.secret_key,
            time_provider=self._time_synchronizer)

    @property
    def name(self) -> str:
        if self._domain == "com":
            return "mexc"
        else:
            return f"mexc_{self._domain}"

    @property
    def rate_limits_rules(self):
        return CONSTANTS.RATE_LIMITS

    @property
    def domain(self):
        return self._domain

    @property
    def client_order_id_max_length(self):
        return CONSTANTS.MAX_ORDER_ID_LEN

    @property
    def client_order_id_prefix(self):
        return CONSTANTS.HBOT_ORDER_ID_PREFIX

    @property
    def trading_rules_request_path(self):
        return CONSTANTS.EXCHANGE_INFO_PATH_URL

    @property
    def trading_pairs_request_path(self):
        return CONSTANTS.EXCHANGE_INFO_PATH_URL

    @property
    def check_network_request_path(self):
        return CONSTANTS.PING_PATH_URL

    @property
    def trading_pairs(self):
        return self._trading_pairs

    @property
    def is_cancel_request_in_exchange_synchronous(self) -> bool:
        return True

    @property
    def is_trading_required(self) -> bool:
        return self._trading_required

    def supported_order_types(self):
        return [OrderType.LIMIT, OrderType.LIMIT_MAKER, OrderType.MARKET]

    def _parse_retry_after_header(self, headers: Dict[str, Any]) -> Optional[float]:
        """
        Parse the Retry-After header from a 429 response.

        :param headers: Response headers
        :return: Retry-after time in seconds, or None if not found
        """
        retry_after = headers.get("Retry-After") or headers.get("retry-after")
        if retry_after:
            try:
                return float(retry_after)
            except (ValueError, TypeError):
                self.logger().warning(f"Invalid Retry-After header value: {retry_after}")
        return None

    def _calculate_backoff_time(self, retry_after: Optional[float] = None) -> float:
        """
        Calculate the backoff time for rate limit handling.

        :param retry_after: Retry-after time from server response
        :return: Backoff time in seconds
        """
        current_time = self._time_synchronizer.time()

        # If we have a retry-after header, use it
        if retry_after is not None:
            backoff_time = max(retry_after, self._min_rate_limit_backoff)
        else:
            # Use exponential backoff
            if current_time - self._last_429_timestamp < 60:  # Within 1 minute of last 429
                self._consecutive_429_count += 1
            else:
                self._consecutive_429_count = 1

            # Exponential backoff: min_backoff * (multiplier ^ consecutive_count)
            backoff_time = min(
                self._min_rate_limit_backoff * (self._rate_limit_backoff_multiplier ** self._consecutive_429_count),
                self._max_rate_limit_backoff
            )

        self._last_429_timestamp = current_time
        self._rate_limit_backoff_time = backoff_time

        return backoff_time

    def _is_rate_limit_error(self, exception: Exception) -> bool:
        """
        Check if the exception is a rate limit error (429).

        :param exception: The exception to check
        :return: True if it's a rate limit error
        """
        error_str = str(exception).lower()
        return "429" in error_str or "too many requests" in error_str

    async def _handle_rate_limit_error(self, exception: Exception, headers: Optional[Dict[str, Any]] = None) -> None:
        """
        Handle rate limit errors with proper backoff and logging.

        :param exception: The rate limit exception
        :param headers: Response headers (may contain Retry-After)
        """
        retry_after = self._parse_retry_after_header(headers or {}) if headers else None
        backoff_time = self._calculate_backoff_time(retry_after)

        self.logger().error(
            f"🚨 MEXC RATE LIMIT EXCEEDED (429) - ORDERS WILL BE SKIPPED! 🚨\n"
            f"   Consecutive 429 errors: {self._consecutive_429_count}\n"
            f"   Backing off for: {backoff_time:.1f} seconds\n"
            f"   Retry-After header: {retry_after}\n"
            f"   Original error: {exception}\n"
            f"   ⚠️  Trading will be paused to prevent IP ban!"
        )

        # Sleep for the calculated backoff time
        await self._sleep(backoff_time)

        # Log resumption
        self.logger().error(
            f"✅ MEXC RATE LIMIT BACKOFF COMPLETE - RESUMING OPERATIONS! ✅\n"
            f"   Backoff duration: {backoff_time:.1f} seconds\n"
            f"   Consecutive 429s handled: {self._consecutive_429_count}\n"
            f"   🎯 Trading operations will now resume normally"
        )

    async def _should_skip_due_to_rate_limit(self) -> bool:
        """
        Check if we should skip operations due to recent rate limiting.

        :return: True if we should skip this cycle
        """
        current_time = self._time_synchronizer.time()

        # If we're still in backoff period, skip
        if self._rate_limit_backoff_time > 0 and current_time - self._last_429_timestamp < self._rate_limit_backoff_time:
            remaining_time = self._rate_limit_backoff_time - (current_time - self._last_429_timestamp)
            self.logger().error(
                f"🚨 MEXC RATE LIMIT ACTIVE - SKIPPING ALL ORDERS! 🚨\n"
                f"   Time remaining in backoff: {remaining_time:.1f} seconds\n"
                f"   Total backoff period: {self._rate_limit_backoff_time:.1f} seconds\n"
                f"   Consecutive 429 errors: {self._consecutive_429_count}\n"
                f"   ⚠️  No orders will be placed until backoff period expires!"
            )
            return True

        return False

    def should_skip_due_to_rate_limit_sync(self) -> bool:
        """
        Synchronous version of rate limit check for use in Cython code.

        :return: True if we should skip operations due to recent rate limiting
        """
        current_time = self._time_synchronizer.time()

        # If we're still in backoff period, skip
        if self._rate_limit_backoff_time > 0 and current_time - self._last_429_timestamp < self._rate_limit_backoff_time:
            remaining_time = self._rate_limit_backoff_time - (current_time - self._last_429_timestamp)
            self.logger().error(
                f"🚨 MEXC RATE LIMIT ACTIVE - SKIPPING ALL ORDERS! 🚨\n"
                f"   Time remaining in backoff: {remaining_time:.1f} seconds\n"
                f"   Total backoff period: {self._rate_limit_backoff_time:.1f} seconds\n"
                f"   Consecutive 429 errors: {self._consecutive_429_count}\n"
                f"   ⚠️  No orders will be placed until backoff period expires!"
            )
            return True

        return False

    async def _api_request(self,
                           path_url: str,
                           method: RESTMethod = RESTMethod.GET,
                           params: Optional[Dict[str, Any]] = None,
                           data: Optional[Dict[str, Any]] = None,
                           is_auth_required: bool = False,
                           return_err: bool = False,
                           limit_id: Optional[str] = None,
                           headers: Optional[Dict[str, Any]] = None,
                           **kwargs) -> Dict[str, Any]:
        """
        Override the base _api_request method to handle 429 rate limit errors.
        """
        # Check if we should skip due to recent rate limiting
        if await self._should_skip_due_to_rate_limit():
            # Skip this refresh cycle to avoid further rate limiting
            raise IOError("Skipping API request due to recent rate limiting")

        try:
            return await super()._api_request(
                path_url=path_url,
                method=method,
                params=params,
                data=data,
                is_auth_required=is_auth_required,
                return_err=return_err,
                limit_id=limit_id,
                headers=headers,
                **kwargs
            )
        except IOError as e:
            if self._is_rate_limit_error(e):
                # Extract headers from the exception if available
                response_headers = getattr(e, 'headers', None)
                await self._handle_rate_limit_error(e, response_headers)
                # Re-raise the exception to let the caller handle it
                raise
            else:
                # Not a rate limit error, re-raise as-is
                raise

    async def get_all_pairs_prices(self) -> List[Dict[str, str]]:
        pairs_prices = await self._api_get(path_url=CONSTANTS.TICKER_BOOK_PATH_URL, headers={"Content-Type": "application/json"})
        return pairs_prices

    async def get_open_orders(self, trading_pair: str) -> List[Dict[str, Any]]:
        """
        Fetch all open orders for a specific trading pair from MEXC /api/v3/openOrders endpoint.
        This provides the current state of all open orders directly from the exchange.

        :param trading_pair: The trading pair to fetch open orders for
        :return: List of open order dictionaries from MEXC
        """
        try:
            symbol = await self.exchange_symbol_associated_to_pair(trading_pair=trading_pair)
            open_orders = await self._api_get(
                path_url=CONSTANTS.OPEN_ORDERS_PATH_URL,
                params={"symbol": symbol},
                is_auth_required=True,
                headers={"Content-Type": "application/json"}
            )
            self.logger().info(f"📊 Fetched {len(open_orders)} open orders from MEXC for {trading_pair}")
            return open_orders
        except Exception as e:
            self.logger().error(f"Failed to fetch open orders for {trading_pair}: {e}")
            return []

    async def cancel_all_orders_for_symbol(self, trading_pair: str) -> List[Dict[str, Any]]:
        """
        Cancel all open orders for a specific trading pair using MEXC's efficient /api/v3/openOrders DELETE endpoint.
        This is much more efficient than canceling orders one by one.

        :param trading_pair: The trading pair to cancel all orders for
        :return: List of canceled order dictionaries from MEXC
        """
        try:
            symbol = await self.exchange_symbol_associated_to_pair(trading_pair=trading_pair)
            self.logger().info(f"🗑️ Canceling ALL orders for {trading_pair} using efficient MEXC endpoint")

            canceled_orders = await self._api_delete(
                path_url=CONSTANTS.CANCEL_ALL_ORDERS_PATH_URL,
                params={"symbol": symbol},
                is_auth_required=True,
                headers={"Content-Type": "application/json"}
            )

            self.logger().info(f"✅ Successfully canceled {len(canceled_orders)} orders for {trading_pair}")
            return canceled_orders
        except Exception as e:
            self.logger().error(f"❌ Failed to cancel all orders for {trading_pair}: {e}")
            return []

    def _is_request_exception_related_to_time_synchronizer(self, request_exception: Exception):
        return str(CONSTANTS.TIMESTAMP_RELATED_ERROR_CODE) in str(
            request_exception
        ) and CONSTANTS.TIMESTAMP_RELATED_ERROR_MESSAGE in str(request_exception)

    def _is_order_not_found_during_status_update_error(self, status_update_exception: Exception) -> bool:
        return str(CONSTANTS.ORDER_NOT_EXIST_ERROR_CODE) in str(
            status_update_exception
        ) and CONSTANTS.ORDER_NOT_EXIST_MESSAGE in str(status_update_exception)

    def _is_order_not_found_during_cancelation_error(self, cancelation_exception: Exception) -> bool:
        return str(CONSTANTS.UNKNOWN_ORDER_ERROR_CODE) in str(
            cancelation_exception
        ) and CONSTANTS.UNKNOWN_ORDER_MESSAGE in str(cancelation_exception)

    def _create_web_assistants_factory(self) -> WebAssistantsFactory:
        return web_utils.build_api_factory(
            throttler=self._throttler,
            time_synchronizer=self._time_synchronizer,
            domain=self._domain,
            auth=self._auth)

    def _create_order_book_data_source(self) -> OrderBookTrackerDataSource:
        return MexcAPIOrderBookDataSource(
            trading_pairs=self._trading_pairs,
            connector=self,
            domain=self.domain,
            api_factory=self._web_assistants_factory)

    def _create_user_stream_data_source(self) -> UserStreamTrackerDataSource:
        return MexcAPIUserStreamDataSource(
            auth=self._auth,
            trading_pairs=self._trading_pairs,
            connector=self,
            api_factory=self._web_assistants_factory,
            domain=self.domain,
        )

    def _get_fee(self,
                 base_currency: str,
                 quote_currency: str,
                 order_type: OrderType,
                 order_side: TradeType,
                 amount: Decimal,
                 price: Decimal = s_decimal_NaN,
                 is_maker: Optional[bool] = None) -> TradeFeeBase:
        is_maker = order_type is OrderType.LIMIT_MAKER
        return DeductedFromReturnsTradeFee(percent=self.estimate_fee_pct(is_maker))

    async def _place_order(self,
                           order_id: str,
                           trading_pair: str,
                           amount: Decimal,
                           trade_type: TradeType,
                           order_type: OrderType,
                           price: Decimal,
                           **kwargs) -> Tuple[str, float]:
        order_result = None
        amount_str = f"{amount:f}"
        type_str = MexcExchange.mexc_order_type(order_type)
        side_str = CONSTANTS.SIDE_BUY if trade_type is TradeType.BUY else CONSTANTS.SIDE_SELL
        symbol = await self.exchange_symbol_associated_to_pair(trading_pair=trading_pair)
        api_params = {"symbol": symbol,
                      "side": side_str,
                      "quantity": amount_str,
                      "type": type_str,
                      "newClientOrderId": order_id}
        if order_type.is_limit_type():
            price_str = f"{price:f}"
            api_params["price"] = price_str
            api_params["timeInForce"] = CONSTANTS.TIME_IN_FORCE_GTC
        try:
            order_result = await self._api_post(
                path_url=CONSTANTS.ORDER_PATH_URL,
                data=api_params,
                is_auth_required=True)
            o_id = str(order_result["orderId"])
            transact_time = order_result["transactTime"] * 1e-3
        except IOError as e:
            error_description = str(e)
            is_server_overloaded = ("status is 503" in error_description
                                    and "Unknown error, please check your request or try again later." in error_description)
            if is_server_overloaded:
                o_id = "UNKNOWN"
                transact_time = self._time_synchronizer.time()
            else:
                raise
        return o_id, transact_time

    async def _place_batch_orders(self, orders_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Place multiple orders in a single batch request using MEXC batch orders API.
        Uses request body approach which works with MEXC's signature validation.

        :param orders_data: List of order dictionaries with order parameters
        :return: List of order results from the batch API
        """
        try:
            # orders_data is already in the correct format from _process_order_batch
            # Use the correct MEXC batch orders approach with proper URL encoding
            batch_orders_json = json.dumps(orders_data, separators=(',', ':'))  # Compact JSON
            timestamp = int(self._time_synchronizer.time() * 1e3)

            # Create signature using URL-encoded parameters (the working approach)
            import hashlib
            import hmac
            from urllib.parse import urlencode

            params = {
                "batchOrders": batch_orders_json,
                "timestamp": timestamp,
                "recvWindow": 5000
            }

            # Generate signature using URL encoding - this is the key to making it work!
            query_string = urlencode(params)
            signature = hmac.new(
                self.secret_key.encode("utf8"),
                query_string.encode("utf8"),
                hashlib.sha256
            ).hexdigest()

            # Log the batch order details for debugging
            self.logger().info(f"MEXC Batch Orders - JSON: {batch_orders_json}")
            self.logger().info(f"MEXC Batch Orders - Query string length: {len(query_string)}")
            self.logger().info(f"MEXC Batch Orders - Signature: {signature[:20]}...")
            self.logger().info(f"MEXC Batch Orders - Fixed format: Using LIMIT type, numeric values, no extra fields")

            # Make the batch orders API call with properly URL-encoded parameters
            # Use the exact URL format from your working test
            full_url = f"https://api.mexc.com/api/v3/batchOrders?{query_string}&signature={signature}"

            # Use aiohttp directly for this special case
            import aiohttp
            headers = {
                "X-MEXC-APIKEY": self.api_key,
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    full_url,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        batch_result = await response.json()
                    else:
                        response_text = await response.text()
                        raise Exception(f"Batch orders failed: {response.status} - {response_text}")

            return batch_result

        except Exception as e:
            self.logger().error(f"Error placing batch orders: {e}")
            raise

    def batch_order_create(self, orders_to_create: List[Union["LimitOrder", "MarketOrder"]]) -> List[Union["LimitOrder", "MarketOrder"]]:
        """
        Issues a batch order creation as a single API request for MEXC exchange.

        :param orders_to_create: A list of LimitOrder or MarketOrder objects representing the orders to create
        :returns: A list of LimitOrder or MarketOrder objects representing the created orders
        """
        from hummingbot.connector.utils import get_new_client_order_id
        from hummingbot.core.data_type.limit_order import LimitOrder
        from hummingbot.core.data_type.market_order import MarketOrder

        # Group orders by symbol and side for optimal batching
        orders_by_symbol_side = {}
        orders_with_ids = []

        for order in orders_to_create:
            # Generate client order ID
            client_order_id = get_new_client_order_id(
                is_buy=order.is_buy,
                trading_pair=order.trading_pair,
                hbot_order_id_prefix=self.client_order_id_prefix,
                max_id_len=self.client_order_id_max_length,
            )
            order_with_id = order.copy_with_id(client_order_id=client_order_id)
            orders_with_ids.append(order_with_id)

            # Group by symbol and side for batching
            key = f"{order.trading_pair}_{order.is_buy}"
            if key not in orders_by_symbol_side:
                orders_by_symbol_side[key] = []
            orders_by_symbol_side[key].append(order_with_id)

        # Execute the simplified 3-step batch order creation
        from hummingbot.core.utils.async_utils import safe_ensure_future
        safe_ensure_future(self._execute_simplified_batch_order_create(orders_with_ids))

        return orders_with_ids

    async def _execute_simplified_batch_order_create(self, orders_to_create: List[Union["LimitOrder", "MarketOrder"]]):
        """
        Execute the simplified 3-step batch order creation:
        1. Fetch current open orders from MEXC
        2. Place new batch orders
        3. Cancel the previously fetched orders

        This approach eliminates complex tracking and prevents duplicate cancellations.
        """
        if not orders_to_create:
            return

        # Get the trading pair (assuming all orders are for the same pair)
        trading_pair = orders_to_create[0].trading_pair

        try:
            # STEP 1: Fetch current open orders from MEXC
            self.logger().info(f"🔍 STEP 1: Fetching current open orders for {trading_pair}")
            current_open_orders = await self.get_open_orders(trading_pair)

            # Extract order IDs for cancellation (use orderId, not clientOrderId)
            orders_to_cancel = [
                order["orderId"] for order in current_open_orders
                if "orderId" in order
            ]

            if orders_to_cancel:
                self.logger().info(f"📋 Found {len(orders_to_cancel)} existing orders to cancel after new orders are placed")
            else:
                self.logger().info(f"✅ No existing orders found for {trading_pair}")

            # STEP 2: Place new batch orders
            self.logger().info(f"🚀 STEP 2: Placing {len(orders_to_create)} new batch orders")
            await self._execute_batch_order_create_only(orders_to_create)

            # STEP 3: Cancel previously fetched orders
            if orders_to_cancel:
                self.logger().info(f"🗑️ STEP 3: Canceling {len(orders_to_cancel)} previously fetched orders")
                await self._cancel_orders_by_exchange_id(orders_to_cancel, trading_pair)
            else:
                self.logger().info(f"✅ STEP 3: No orders to cancel")

            self.logger().info(f"🎉 Simplified batch order creation completed for {trading_pair}")

        except Exception as e:
            self.logger().error(f"❌ Simplified batch order creation failed: {e}")
            # Fallback to individual order placement
            await self._fallback_to_individual_orders(orders_to_create)

    async def _execute_batch_order_create_only(self, orders_to_create: List[Union["LimitOrder", "MarketOrder"]]):
        """
        Execute only the batch order creation part (Step 2 of the simplified approach).
        """
        from hummingbot.core.data_type.limit_order import LimitOrder

        # Group orders by symbol and side (max 20 orders per batch)
        buy_orders = [order for order in orders_to_create if order.is_buy]
        sell_orders = [order for order in orders_to_create if not order.is_buy]

        # Process buy orders in batches
        if buy_orders:
            await self._process_order_batch(buy_orders, "BUY")

        # Process sell orders in batches
        if sell_orders:
            await self._process_order_batch(sell_orders, "SELL")

    async def _cancel_orders_by_exchange_id(self, exchange_order_ids: List[str], trading_pair: str):
        """
        Cancel orders using their exchange order IDs (Step 3 of the simplified approach).
        This is much simpler than the complex tracking system.
        """
        if not exchange_order_ids:
            return

        symbol = await self.exchange_symbol_associated_to_pair(trading_pair=trading_pair)

        # Cancel orders individually with rate limiting
        for i, exchange_order_id in enumerate(exchange_order_ids):
            try:
                api_params = {
                    "symbol": symbol,
                    "orderId": exchange_order_id,
                }

                cancel_result = await self._api_delete(
                    path_url=CONSTANTS.ORDER_PATH_URL,
                    params=api_params,
                    is_auth_required=True
                )

                if cancel_result.get("status") == "CANCELED":
                    self.logger().info(f"✅ Successfully canceled order {exchange_order_id}")
                else:
                    self.logger().warning(f"⚠️ Order {exchange_order_id} cancel result: {cancel_result}")

            except Exception as e:
                # If we get a 400 error, the order might already be canceled/filled - don't retry
                if "400" in str(e):
                    self.logger().info(f"🔄 Order {exchange_order_id} already canceled/filled (400 error) - skipping")
                else:
                    self.logger().error(f"❌ Failed to cancel order {exchange_order_id}: {e}")

            # Rate limiting: max 10 orders per second
            if (i + 1) % 10 == 0 and i + 1 < len(exchange_order_ids):
                self.logger().info(f"⏳ Rate limiting: waiting 1 second after canceling {i + 1} orders...")
                await self._sleep(1.0)

    async def _fallback_to_individual_orders(self, orders_to_create: List[Union["LimitOrder", "MarketOrder"]]):
        """
        Fallback method to place orders individually when batch creation fails.
        """
        self.logger().info(f"🔄 Falling back to individual order placement for {len(orders_to_create)} orders")

        for order in orders_to_create:
            try:
                await self._place_individual_order_fallback(order)
            except Exception as e:
                self.logger().error(f"❌ Individual order fallback failed for {order.client_order_id}: {e}")

    async def _process_order_batch(self, orders: List[Union["LimitOrder", "MarketOrder"]], side: str):
        """
        Process a batch of orders for a specific side (BUY/SELL).
        """
        from hummingbot.core.data_type.limit_order import LimitOrder

        # Split into chunks of max 20 orders (MEXC batch limit)
        batch_size = 20
        for i in range(0, len(orders), batch_size):
            batch = orders[i:i + batch_size]

            try:
                # Prepare batch order data - EXACTLY match the working test format
                orders_data = []
                for order in batch:
                    symbol = await self.exchange_symbol_associated_to_pair(trading_pair=order.trading_pair)
                    # Create order data EXACTLY like the working test - only 5 fields, numeric values
                    order_data = {
                        "symbol": symbol,
                        "side": CONSTANTS.SIDE_BUY if order.is_buy else CONSTANTS.SIDE_SELL,
                        "type": "LIMIT",  # Use MEXC format: "LIMIT" not "LIMIT_ORDER"
                        "quantity": float(order.quantity),  # Use numeric value, not string
                        "price": float(order.price) if isinstance(order, LimitOrder) else 0.0  # Use numeric value, not string
                    }
                    # DO NOT add newClientOrderId or timeInForce - the working test doesn't have them!
                    # These extra fields cause the 404 error
                    orders_data.append(order_data)

                # Execute batch order
                batch_result = await self._place_batch_orders(orders_data)

                # Process batch results
                self.logger().info(f"Batch result type: {type(batch_result)}, content: {batch_result}")
                if isinstance(batch_result, list):
                    for i, result in enumerate(batch_result):
                        if i < len(batch):
                            order = batch[i]
                            if "orderId" in result:
                                # Success - track the order
                                exchange_order_id = str(result["orderId"])
                                self.start_tracking_order(
                                    order_id=order.client_order_id,
                                    exchange_order_id=exchange_order_id,
                                    trading_pair=order.trading_pair,
                                    trade_type=TradeType.BUY if order.is_buy else TradeType.SELL,
                                    price=order.price if hasattr(order, 'price') else Decimal('0'),
                                    amount=order.quantity,
                                    order_type=OrderType.LIMIT if isinstance(order, LimitOrder) else OrderType.MARKET
                                )
                                self.logger().info(f"Batch order created and tracked: {order.client_order_id} -> {exchange_order_id}")
                            else:
                                # Error in this specific order
                                error_msg = result.get("msg", "Unknown error")
                                self.logger().error(f"Batch order failed for {order.client_order_id}: {error_msg}")
                else:
                    self.logger().error(f"Unexpected batch result format: {batch_result}")

            except Exception as e:
                self.logger().error(f"Batch order failed, falling back to individual orders: {e}")
                # Fallback to individual order placement
                for order in batch:
                    try:
                        await self._place_individual_order_fallback(order)
                    except Exception as individual_error:
                        self.logger().error(f"Individual order fallback also failed for {order.client_order_id}: {individual_error}")

    async def _place_individual_order_fallback(self, order: Union["LimitOrder", "MarketOrder"]):
        """
        Fallback method to place individual orders when batch fails.
        """
        from hummingbot.core.data_type.limit_order import LimitOrder

        try:
            exchange_order_id, timestamp = await self._place_order(
                order_id=order.client_order_id,
                trading_pair=order.trading_pair,
                amount=order.quantity,
                trade_type=TradeType.BUY if order.is_buy else TradeType.SELL,
                order_type=OrderType.LIMIT if isinstance(order, LimitOrder) else OrderType.MARKET,
                price=order.price if hasattr(order, 'price') else Decimal('0')
            )

            self.logger().info(f"Individual fallback order created: {order.client_order_id}")

        except Exception as e:
            self.logger().error(f"Individual fallback order failed for {order.client_order_id}: {e}")
            raise

    async def _place_cancel(self, order_id: str, tracked_order: InFlightOrder):
        symbol = await self.exchange_symbol_associated_to_pair(trading_pair=tracked_order.trading_pair)

        # For batch orders, use exchange order ID (orderId) instead of client order ID
        # because MEXC doesn't recognize our client order IDs for batch orders
        if tracked_order.exchange_order_id:
            api_params = {
                "symbol": symbol,
                "orderId": tracked_order.exchange_order_id,  # Use exchange order ID
            }
            self.logger().info(f"Canceling order using exchange order ID: {tracked_order.exchange_order_id}")
        else:
            # Fallback to client order ID for individual orders
            api_params = {
                "symbol": symbol,
                "origClientOrderId": order_id,
            }
            self.logger().info(f"Canceling order using client order ID: {order_id}")

        cancel_result = await self._api_delete(
            path_url=CONSTANTS.ORDER_PATH_URL,
            params=api_params,
            is_auth_required=True)
        if cancel_result.get("status") == "CANCELED":
            return True
        return False

    async def _format_trading_rules(self, exchange_info_dict: Dict[str, Any]) -> List[TradingRule]:
        trading_pair_rules = exchange_info_dict.get("symbols", [])
        retval = []
        for rule in filter(mexc_utils.is_exchange_information_valid, trading_pair_rules):
            try:
                trading_pair = await self.trading_pair_associated_to_exchange_symbol(symbol=rule.get("symbol"))
                min_order_size = Decimal(rule.get("baseSizePrecision"))
                min_price_inc = Decimal(f"1e-{rule['quotePrecision']}")
                min_amount_inc = Decimal(f"1e-{rule['baseAssetPrecision']}")
                min_notional = Decimal(rule['quoteAmountPrecision'])
                retval.append(
                    TradingRule(trading_pair,
                                min_order_size=min_order_size,
                                min_price_increment=min_price_inc,
                                min_base_amount_increment=min_amount_inc,
                                min_notional_size=min_notional))

            except Exception:
                self.logger().exception(f"Error parsing the trading pair rule {rule}. Skipping.")
        return retval

    async def _status_polling_loop_fetch_updates(self):
        await self._update_order_fills_from_trades()
        await super()._status_polling_loop_fetch_updates()

    async def _update_trading_fees(self):
        """
        Update fees information from the exchange
        """
        pass

    async def _user_stream_event_listener(self):
        """
        Listens to messages from _user_stream_tracker.user_stream queue.
        Traders, Orders, and Balance updates from the WS.
        """
        user_channels = [
            CONSTANTS.USER_TRADES_ENDPOINT_NAME,
            CONSTANTS.USER_ORDERS_ENDPOINT_NAME,
            CONSTANTS.USER_BALANCE_ENDPOINT_NAME,
        ]
        async for event_message in self._iter_user_event_queue():
            try:
                channel: str = event_message.get("c", None)
                results: Dict[str, Any] = event_message.get("d", {})
                if "code" not in event_message and channel not in user_channels:
                    self.logger().error(
                        f"Unexpected message in user stream: {event_message}.", exc_info=True)
                    continue
                if channel == CONSTANTS.USER_TRADES_ENDPOINT_NAME:
                    self._process_trade_message(results)
                elif channel == CONSTANTS.USER_ORDERS_ENDPOINT_NAME:
                    self._process_order_message(event_message)
                elif channel == CONSTANTS.USER_BALANCE_ENDPOINT_NAME:
                    self._process_balance_message_ws(results)

            except asyncio.CancelledError:
                raise
            except Exception:
                self.logger().error(
                    "Unexpected error in user stream listener loop.", exc_info=True)
                await self._sleep(5.0)

    def _process_balance_message_ws(self, account):
        asset_name = account["a"]
        self._account_available_balances[asset_name] = Decimal(str(account["f"]))
        self._account_balances[asset_name] = Decimal(str(account["f"])) + Decimal(str(account["l"]))

    def _create_trade_update_with_order_fill_data(
            self,
            order_fill: Dict[str, Any],
            order: InFlightOrder):

        fee = TradeFeeBase.new_spot_fee(
            fee_schema=self.trade_fee_schema(),
            trade_type=order.trade_type,
            percent_token=order_fill["N"],
            flat_fees=[TokenAmount(
                amount=Decimal(order_fill["n"]),
                token=order_fill["N"]
            )]
        )
        trade_update = TradeUpdate(
            trade_id=str(order_fill["t"]),
            client_order_id=order.client_order_id,
            exchange_order_id=order.exchange_order_id,
            trading_pair=order.trading_pair,
            fee=fee,
            fill_base_amount=Decimal(order_fill["v"]),
            fill_quote_amount=Decimal(order_fill["a"]),
            fill_price=Decimal(order_fill["p"]),
            fill_timestamp=order_fill["T"] * 1e-3,
        )
        return trade_update

    def _process_trade_message(self, trade: Dict[str, Any], client_order_id: Optional[str] = None):
        client_order_id = client_order_id or str(trade["c"])
        tracked_order = self._order_tracker.all_fillable_orders.get(client_order_id)
        if tracked_order is None:
            self.logger().debug(f"Ignoring trade message with id {client_order_id}: not in in_flight_orders.")
        else:
            trade_update = self._create_trade_update_with_order_fill_data(
                order_fill=trade,
                order=tracked_order)
            self._order_tracker.process_trade_update(trade_update)

    def _create_order_update_with_order_status_data(self, order_status: Dict[str, Any], order: InFlightOrder):
        client_order_id = str(order_status["d"].get("c", ""))
        order_update = OrderUpdate(
            trading_pair=order.trading_pair,
            update_timestamp=int(order_status["t"] * 1e-3),
            new_state=CONSTANTS.WS_ORDER_STATE[order_status["d"]["s"]],
            client_order_id=client_order_id,
            exchange_order_id=str(order_status["d"]["i"]),
        )
        return order_update

    def _process_order_message(self, raw_msg: Dict[str, Any]):
        order_msg = raw_msg.get("d", {})
        client_order_id = str(order_msg.get("c", ""))
        tracked_order = self._order_tracker.all_updatable_orders.get(client_order_id)
        if not tracked_order:
            self.logger().debug(f"Ignoring order message with id {client_order_id}: not in in_flight_orders.")
            return

        order_update = self._create_order_update_with_order_status_data(order_status=raw_msg, order=tracked_order)
        self._order_tracker.process_order_update(order_update=order_update)

    async def _update_order_fills_from_trades(self):
        """
        This is intended to be a backup measure to get filled events with trade ID for orders,
        in case Mexc's user stream events are not working.
        NOTE: It is not required to copy this functionality in other connectors.
        This is separated from _update_order_status which only updates the order status without producing filled
        events, since Mexc's get order endpoint does not return trade IDs.
        The minimum poll interval for order status is 10 seconds.
        """
        small_interval_last_tick = self._last_poll_timestamp / self.UPDATE_ORDER_STATUS_MIN_INTERVAL
        small_interval_current_tick = self.current_timestamp / self.UPDATE_ORDER_STATUS_MIN_INTERVAL
        long_interval_last_tick = self._last_poll_timestamp / self.LONG_POLL_INTERVAL
        long_interval_current_tick = self.current_timestamp / self.LONG_POLL_INTERVAL

        if (long_interval_current_tick > long_interval_last_tick
                or (self.in_flight_orders and small_interval_current_tick > small_interval_last_tick)):
            query_time = int(self._last_trades_poll_mexc_timestamp * 1e3)
            self._last_trades_poll_mexc_timestamp = self._time_synchronizer.time()
            order_by_exchange_id_map = {}
            for order in self._order_tracker.all_fillable_orders.values():
                order_by_exchange_id_map[order.exchange_order_id] = order

            tasks = []
            trading_pairs = self.trading_pairs
            for trading_pair in trading_pairs:
                params = {
                    "symbol": await self.exchange_symbol_associated_to_pair(trading_pair=trading_pair)
                }
                if self._last_poll_timestamp > 0:
                    params["startTime"] = query_time
                tasks.append(self._api_get(
                    path_url=CONSTANTS.MY_TRADES_PATH_URL,
                    params=params,
                    is_auth_required=True,
                    headers={"Content-Type": "application/json"}))

            self.logger().debug(f"Polling for order fills of {len(tasks)} trading pairs.")
            results = await safe_gather(*tasks, return_exceptions=True)

            for trades, trading_pair in zip(results, trading_pairs):

                if isinstance(trades, Exception):
                    self.logger().network(
                        f"Error fetching trades update for the order {trading_pair}: {trades}.",
                        app_warning_msg=f"Failed to fetch trade update for {trading_pair}."
                    )
                    continue
                for trade in trades:
                    exchange_order_id = str(trade["orderId"])
                    if exchange_order_id in order_by_exchange_id_map:
                        # This is a fill for a tracked order
                        tracked_order = order_by_exchange_id_map[exchange_order_id]
                        fee = TradeFeeBase.new_spot_fee(
                            fee_schema=self.trade_fee_schema(),
                            trade_type=tracked_order.trade_type,
                            percent_token=trade["commissionAsset"],
                            flat_fees=[TokenAmount(amount=Decimal(trade["commission"]), token=trade["commissionAsset"])]
                        )
                        trade_update = TradeUpdate(
                            trade_id=str(trade["id"]),
                            client_order_id=tracked_order.client_order_id,
                            exchange_order_id=exchange_order_id,
                            trading_pair=trading_pair,
                            fee=fee,
                            fill_base_amount=Decimal(trade["qty"]),
                            fill_quote_amount=Decimal(trade["quoteQty"]),
                            fill_price=Decimal(trade["price"]),
                            fill_timestamp=trade["time"] * 1e-3,
                        )
                        self._order_tracker.process_trade_update(trade_update)
                    elif self.is_confirmed_new_order_filled_event(str(trade["id"]), exchange_order_id, trading_pair):
                        # This is a fill of an order registered in the DB but not tracked any more
                        self._current_trade_fills.add(TradeFillOrderDetails(
                            market=self.display_name,
                            exchange_trade_id=str(trade["id"]),
                            symbol=trading_pair))
                        self.trigger_event(
                            MarketEvent.OrderFilled,
                            OrderFilledEvent(
                                timestamp=float(trade["time"]) * 1e-3,
                                order_id=self._exchange_order_ids.get(str(trade["orderId"]), None),
                                trading_pair=trading_pair,
                                trade_type=TradeType.BUY if trade["isBuyer"] else TradeType.SELL,
                                order_type=OrderType.LIMIT_MAKER if trade["isMaker"] else OrderType.LIMIT,
                                price=Decimal(trade["price"]),
                                amount=Decimal(trade["qty"]),
                                trade_fee=DeductedFromReturnsTradeFee(
                                    flat_fees=[
                                        TokenAmount(
                                            trade["commissionAsset"],
                                            Decimal(trade["commission"])
                                        )
                                    ]
                                ),
                                exchange_trade_id=str(trade["id"])
                            ))
                        self.logger().info(f"Recreating missing trade in TradeFill: {trade}")

    async def _all_trade_updates_for_order(self, order: InFlightOrder) -> List[TradeUpdate]:
        trade_updates = []

        if order.exchange_order_id is not None:
            exchange_order_id = order.exchange_order_id
            trading_pair = await self.exchange_symbol_associated_to_pair(trading_pair=order.trading_pair)
            all_fills_response = await self._api_get(
                path_url=CONSTANTS.MY_TRADES_PATH_URL,
                params={
                    "symbol": trading_pair,
                    "orderId": exchange_order_id
                },
                is_auth_required=True,
                limit_id=CONSTANTS.MY_TRADES_PATH_URL,
                headers={"Content-Type": "application/json"})

            for trade in all_fills_response:
                exchange_order_id = str(trade["orderId"])
                fee = TradeFeeBase.new_spot_fee(
                    fee_schema=self.trade_fee_schema(),
                    trade_type=order.trade_type,
                    percent_token=trade["commissionAsset"],
                    flat_fees=[TokenAmount(amount=Decimal(trade["commission"]), token=trade["commissionAsset"])]
                )
                trade_update = TradeUpdate(
                    trade_id=str(trade["id"]),
                    client_order_id=order.client_order_id,
                    exchange_order_id=exchange_order_id,
                    trading_pair=trading_pair,
                    fee=fee,
                    fill_base_amount=Decimal(trade["qty"]),
                    fill_quote_amount=Decimal(trade["quoteQty"]),
                    fill_price=Decimal(trade["price"]),
                    fill_timestamp=trade["time"] * 1e-3,
                )
                trade_updates.append(trade_update)

        return trade_updates

    async def _request_order_status(self, tracked_order: InFlightOrder) -> OrderUpdate:
        trading_pair = await self.exchange_symbol_associated_to_pair(trading_pair=tracked_order.trading_pair)
        updated_order_data = await self._api_get(
            path_url=CONSTANTS.ORDER_PATH_URL,
            params={
                "symbol": trading_pair,
                "origClientOrderId": tracked_order.client_order_id},
            is_auth_required=True,
            headers={"Content-Type": "application/json"})

        new_state = CONSTANTS.ORDER_STATE[updated_order_data["status"]]

        order_update = OrderUpdate(
            client_order_id=tracked_order.client_order_id,
            exchange_order_id=str(updated_order_data["orderId"]),
            trading_pair=tracked_order.trading_pair,
            update_timestamp=updated_order_data["updateTime"] * 1e-3,
            new_state=new_state,
        )

        return order_update

    async def _update_balances(self):
        local_asset_names = set(self._account_balances.keys())
        remote_asset_names = set()

        account_info = await self._api_get(
            path_url=CONSTANTS.ACCOUNTS_PATH_URL,
            is_auth_required=True,
            headers={"Content-Type": "application/json"})

        balances = account_info["balances"]
        for balance_entry in balances:
            asset_name = balance_entry["asset"]
            free_balance = Decimal(balance_entry["free"])
            total_balance = Decimal(balance_entry["free"]) + Decimal(balance_entry["locked"])
            self._account_available_balances[asset_name] = free_balance
            self._account_balances[asset_name] = total_balance
            remote_asset_names.add(asset_name)

        asset_names_to_remove = local_asset_names.difference(remote_asset_names)
        for asset_name in asset_names_to_remove:
            del self._account_available_balances[asset_name]
            del self._account_balances[asset_name]

    def _initialize_trading_pair_symbols_from_exchange_info(self, exchange_info: Dict[str, Any]):
        mapping = bidict()
        for symbol_data in filter(mexc_utils.is_exchange_information_valid, exchange_info["symbols"]):
            mapping[symbol_data["symbol"]] = combine_to_hb_trading_pair(base=symbol_data["baseAsset"],
                                                                        quote=symbol_data["quoteAsset"])
        self._set_trading_pair_symbol_map(mapping)

    async def _get_last_traded_price(self, trading_pair: str) -> float:
        params = {
            "symbol": await self.exchange_symbol_associated_to_pair(trading_pair=trading_pair)
        }

        resp_json = await self._api_request(
            method=RESTMethod.GET,
            path_url=CONSTANTS.TICKER_PRICE_CHANGE_PATH_URL,
            params=params,
            headers={"Content-Type": "application/json"}
        )

        return float(resp_json["lastPrice"])

    async def _make_network_check_request(self):
        await self._api_get(path_url=self.check_network_request_path, headers={"Content-Type": "application/json"})

    async def _make_trading_rules_request(self) -> Any:
        exchange_info = await self._api_get(path_url=self.trading_rules_request_path, headers={"Content-Type": "application/json"})
        return exchange_info

    async def _make_trading_pairs_request(self) -> Any:
        exchange_info = await self._api_get(path_url=self.trading_pairs_request_path, headers={"Content-Type": "application/json"})
        return exchange_info

    async def cancel_orders_rate_limited(self, order_ids: List[str]) -> None:
        """
        Cancel multiple orders with rate limiting to respect MEXC's 10 orders/second limit.

        :param order_ids: List of order IDs to cancel
        """
        if not order_ids:
            return

        current_time = self._time_synchronizer.time()

        # Add orders to the cancel queue
        self._cancel_order_queue.extend(order_ids)

        # Process the queue in batches of 10 orders per second
        while self._cancel_order_queue:
            # Check if enough time has passed since last batch
            time_since_last_batch = current_time - self._last_cancel_batch_time

            if time_since_last_batch < self._cancel_batch_interval:
                # Wait for the remaining time
                wait_time = self._cancel_batch_interval - time_since_last_batch
                self.logger().error(
                    f"🕒 MEXC CANCEL RATE LIMIT - WAITING FOR NEXT BATCH! 🕒\n"
                    f"   Waiting time: {wait_time:.1f} seconds\n"
                    f"   Remaining orders to cancel: {len(self._cancel_order_queue)}\n"
                    f"   Rate limit: {self._cancel_orders_per_second} orders/second\n"
                    f"   ⚠️  Cancellation paused to prevent rate limit violations!"
                )
                await self._sleep(wait_time)
                current_time = self._time_synchronizer.time()

            # Process up to 10 orders in this batch
            batch_size = min(self._cancel_orders_per_second, len(self._cancel_order_queue))
            current_batch = self._cancel_order_queue[:batch_size]
            self._cancel_order_queue = self._cancel_order_queue[batch_size:]

            self.logger().error(
                f"📤 MEXC CANCELING BATCH OF {len(current_batch)} ORDERS! 📤\n"
                f"   Orders in this batch: {len(current_batch)}\n"
                f"   Remaining in queue: {len(self._cancel_order_queue)}\n"
                f"   Rate limit: {self._cancel_orders_per_second} orders/second"
            )

            # Cancel orders in the current batch
            cancel_tasks = []
            for order_id in current_batch:
                try:
                    tracked_order = self._order_tracker.all_updatable_orders.get(order_id)
                    if tracked_order:
                        cancel_task = self._place_cancel(order_id, tracked_order)
                        cancel_tasks.append(cancel_task)
                    else:
                        self.logger().warning(f"Order {order_id} not found in tracked orders, skipping cancel")
                except Exception as e:
                    self.logger().error(f"Error preparing cancel for order {order_id}: {e}")

            # Execute all cancellations in the batch concurrently
            if cancel_tasks:
                try:
                    from hummingbot.core.utils.async_utils import safe_gather
                    results = await safe_gather(*cancel_tasks, return_exceptions=True)

                    # Log results
                    successful_cancels = sum(1 for result in results if not isinstance(result, Exception))
                    failed_cancels = len(results) - successful_cancels

                    if failed_cancels > 0:
                        self.logger().error(f"❌ Batch cancel results: {successful_cancels} successful, {failed_cancels} failed")
                        # Log specific failures and clean up tracking
                        for i, result in enumerate(results):
                            if isinstance(result, Exception):
                                order_id = current_batch[i] if i < len(current_batch) else "unknown"
                                # Clean up tracking for failed cancellations
                                self._orders_being_canceled.discard(order_id)
                                self.logger().error(f"Cancel failed for order {order_id}: {result}")
                    else:
                        # Clean up tracking for successful cancellations
                        for i, result in enumerate(results):
                            if not isinstance(result, Exception):
                                order_id = current_batch[i] if i < len(current_batch) else "unknown"
                                self._orders_being_canceled.discard(order_id)
                        self.logger().info(f"✅ Successfully canceled {successful_cancels} orders in batch")

                except Exception as e:
                    self.logger().error(f"Error executing cancel batch: {e}")

            # Update the last batch time
            self._last_cancel_batch_time = current_time

            # If there are more orders to cancel, update current time for next iteration
            if self._cancel_order_queue:
                current_time = self._time_synchronizer.time()

    def queue_orders_for_cancellation(self, order_ids: List[str]) -> None:
        """
        Queue orders for rate-limited cancellation. This is a synchronous method
        that can be called from Cython code.

        :param order_ids: List of order IDs to queue for cancellation
        """
        if not order_ids:
            return

        self._cancel_order_queue.extend(order_ids)

        self.logger().error(
            f"📋 MEXC ORDERS QUEUED FOR RATE-LIMITED CANCELLATION! 📋\n"
            f"   Orders queued: {len(order_ids)}\n"
            f"   Total in queue: {len(self._cancel_order_queue)}\n"
            f"   Rate limit: {self._cancel_orders_per_second} orders/second\n"
            f"   ⚠️  Cancellations will be processed in batches to prevent rate limiting!"
        )

        # Start processing the queue asynchronously
        from hummingbot.core.utils.async_utils import safe_ensure_future
        safe_ensure_future(self._process_cancel_queue())

    async def _process_cancel_queue(self) -> None:
        """
        Process the cancel order queue with rate limiting.
        """
        if not self._cancel_order_queue:
            return

        # Extract all orders from queue to process
        orders_to_cancel = self._cancel_order_queue.copy()
        self._cancel_order_queue.clear()

        # Process with rate limiting
        await self.cancel_orders_rate_limited(orders_to_cancel)

    async def cancel_all(self, timeout_seconds: float) -> List[CancellationResult]:
        """
        Override cancel_all to use MEXC's efficient cancel all orders endpoint.
        This is much faster than canceling orders one by one.

        :param timeout_seconds: The timeout at which the operation will be canceled.
        :returns List of CancellationResult which indicates whether each order is successfully canceled.
        """
        # Get all active orders
        active_orders = list(self.in_flight_orders.values())
        if not active_orders:
            return []

        # Group orders by trading pair for efficient cancellation
        orders_by_pair = {}
        for order in active_orders:
            trading_pair = order.trading_pair
            if trading_pair not in orders_by_pair:
                orders_by_pair[trading_pair] = []
            orders_by_pair[trading_pair].append(order)

        self.logger().info(
            f"🚨 MEXC CANCEL ALL ORDERS - USING EFFICIENT ENDPOINT! 🚨\n"
            f"   Total orders to cancel: {len(active_orders)}\n"
            f"   Trading pairs: {list(orders_by_pair.keys())}\n"
            f"   Using /api/v3/openOrders DELETE for instant cancellation\n"
            f"   ⚡ This is much faster than individual cancellations!"
        )

        results = []

        # Cancel all orders for each trading pair using efficient endpoint
        for trading_pair, orders in orders_by_pair.items():
            try:
                canceled_orders = await self.cancel_all_orders_for_symbol(trading_pair)

                # Create success results for all orders in this trading pair
                for order in orders:
                    results.append(CancellationResult(order.client_order_id, True))

                self.logger().info(f"✅ Successfully canceled {len(orders)} orders for {trading_pair}")

            except Exception as e:
                self.logger().error(f"❌ Failed to cancel orders for {trading_pair}: {e}")

                # Create failure results for all orders in this trading pair
                for order in orders:
                    results.append(CancellationResult(order.client_order_id, False))

        success_count = sum(1 for result in results if result.success)
        self.logger().info(f"Cancel all completed: {success_count}/{len(results)} orders canceled successfully")

        return results
