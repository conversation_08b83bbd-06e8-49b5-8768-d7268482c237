from decimal import Decimal

from hummingbot.client.config.config_validators import validate_bool
from hummingbot.client.config.config_var import ConfigVar

# Returns a market prompt that incorporates the connector value set by the user


def market_prompt() -> str:
    connector = price_support_config_map.get("connector").value
    return f'Enter the token trading pair on {connector} >>> '


def validate_target_price_pct(value: str) -> str:
    """Validate target price percentage is positive or zero (for auto-set)"""
    try:
        decimal_value = Decimal(value)
        if decimal_value < 0:
            return "Target price percentage must be greater than or equal to 0 (0 = auto-set to 5%)"
        if decimal_value > 100:
            return "Target price percentage should be reasonable (e.g., 20 for 20% increase)"
    except Exception:
        return "Invalid decimal format"
    return None


def validate_budget(value: str) -> str:
    """Validate budget is positive"""
    try:
        decimal_value = Decimal(value)
        if decimal_value <= 0:
            return "Budget must be greater than 0"
    except Exception:
        return "Invalid decimal format"
    return None


def validate_stop_loss_pct(value: str) -> str:
    """Validate stop loss percentage is reasonable"""
    try:
        decimal_value = Decimal(value)
        if decimal_value < 0:
            return "Stop loss percentage must be greater than or equal to 0"
        if decimal_value > 50:
            return "Stop loss percentage should be reasonable (e.g., 10 for 10%)"
    except Exception:
        return "Invalid decimal format"
    return None


def validate_support_layers(value: str) -> str:
    """Validate number of support layers is reasonable"""
    try:
        int_value = int(value)
        if int_value < 1:
            return "Number of support layers must be at least 1"
        if int_value > 10:
            return "Number of support layers should be reasonable (1-10)"
    except Exception:
        return "Invalid integer format"
    return None


def validate_ratio(value: str) -> str:
    """Validate ratio is positive"""
    try:
        decimal_value = Decimal(value)
        if decimal_value <= 0:
            return "Ratio must be greater than 0"
        if decimal_value > 20:
            return "Ratio should be reasonable (e.g., 4.0 for 4:1 ratio)"
    except Exception:
        return "Invalid decimal format"
    return None


def validate_percentage(value: str) -> str:
    """Validate percentage is reasonable"""
    try:
        decimal_value = Decimal(value)
        if decimal_value < 0:
            return "Percentage must be greater than or equal to 0"
        if decimal_value > 50:
            return "Percentage should be reasonable (e.g., 2.0 for 2%)"
    except Exception:
        return "Invalid decimal format"
    return None


def validate_multiplier(value: str) -> str:
    """Validate multiplier is reasonable"""
    try:
        decimal_value = Decimal(value)
        if decimal_value < 1:
            return "Multiplier must be at least 1.0"
        if decimal_value > 10:
            return "Multiplier should be reasonable (1.0-10.0)"
    except Exception:
        return "Invalid decimal format"
    return None


def validate_time_seconds(value: str) -> str:
    """Validate time in seconds is reasonable"""
    try:
        float_value = float(value)
        if float_value < 0:
            return "Time must be greater than or equal to 0"
        if float_value > 3600:
            return "Time should be reasonable (0-3600 seconds)"
    except Exception:
        return "Invalid float format"
    return None


def validate_mnemonic(value: str) -> str:
    """Validate mnemonic phrase format"""
    if not value.strip():
        return None  # Empty is allowed for simulation mode

    words = value.strip().split()
    if len(words) != 12:
        return "Mnemonic phrase must contain exactly 12 words"

    # Basic validation - check for common issues
    for word in words:
        if not word.isalpha():
            return "Mnemonic words should contain only letters"
        if len(word) < 3:
            return "Mnemonic words should be at least 3 characters long"

    return None


def validate_derivation_path(value: str) -> str:
    """Validate BIP44 derivation path format"""
    if not value.strip():
        return "Derivation path cannot be empty"

    # Basic BIP44 path validation
    if not value.startswith("m/"):
        return "Derivation path must start with 'm/'"

    # Check for basic BIP44 format: m/44'/60'/0'/0/0
    parts = value.split("/")
    if len(parts) != 6:
        return "Invalid derivation path format. Expected: m/44'/60'/0'/0/0"

    return None

# List of parameters defined by the strategy


price_support_config_map = {
    "strategy":
        ConfigVar(key="strategy",
                  prompt="",
                  default="price_support",
                  ),
    "connector":
        ConfigVar(key="connector",
                  prompt="Enter the name of the exchange >>> ",
                  prompt_on_new=True,
                  ),
    "market":
        ConfigVar(key="market",
                  prompt=market_prompt,
                  prompt_on_new=True,
                  ),
    "target_price_pct":
        ConfigVar(key="target_price_pct",
                  prompt="Enter the target price increase percentage (e.g., 20 for 20% increase, 0 for auto-set 5%) >>> ",
                  type_str="decimal",
                  validator=validate_target_price_pct,
                  default=Decimal("0"),
                  prompt_on_new=True,
                  ),
    "max_budget":
        ConfigVar(key="max_budget",
                  prompt="Enter the maximum budget for price support (USD) >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("1000"),
                  prompt_on_new=True,
                  ),
    "stop_loss_pct":
        ConfigVar(key="stop_loss_pct",
                  prompt="Enter the stop loss percentage (e.g., 10 for 10%) >>> ",
                  type_str="decimal",
                  validator=validate_stop_loss_pct,
                  default=Decimal("5.0"),
                  prompt_on_new=True,
                  ),
    "uniswap_token_address":
        ConfigVar(key="uniswap_token_address",
                  prompt="Enter the Uniswap token contract address >>> ",
                  default="0x9e12735d77c72c5C3670636D428f2F3815d8A4cB",
                  prompt_on_new=True,
                  ),
    "analysis_interval":
        ConfigVar(key="analysis_interval",
                  prompt="Enter the market analysis interval in seconds >>> ",
                  type_str="float",
                  default=30.0,
                  prompt_on_new=False,
                  ),
    "coordination_enabled":
        ConfigVar(key="coordination_enabled",
                  prompt="Enable coordination with pure market making strategy? (Yes/No) >>> ",
                  type_str="bool",
                  validator=validate_bool,
                  default=True,
                  prompt_on_new=False,
                  ),
    "emergency_stop_enabled":
        ConfigVar(key="emergency_stop_enabled",
                  prompt="Enable emergency stop protection? (Yes/No) >>> ",
                  type_str="bool",
                  validator=validate_bool,
                  default=True,
                  prompt_on_new=False,
                  ),
    "max_daily_loss":
        ConfigVar(key="max_daily_loss",
                  prompt="Enter the maximum daily loss limit (USD) >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("500"),
                  prompt_on_new=True,
                  ),
    "min_order_size":
        ConfigVar(key="min_order_size",
                  prompt="Enter the minimum order size (USD) >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("10"),
                  prompt_on_new=False,
                  ),
    "max_order_size":
        ConfigVar(key="max_order_size",
                  prompt="Enter the maximum order size (USD) >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("200"),
                  prompt_on_new=False,
                  ),
    # Enhanced strategy parameters
    "support_layers":
        ConfigVar(key="support_layers",
                  prompt="Enter the number of support layers to build below current price (1-10) >>> ",
                  type_str="int",
                  validator=validate_support_layers,
                  default=5,
                  prompt_on_new=True,
                  ),
    "support_to_push_ratio":
        ConfigVar(key="support_to_push_ratio",
                  prompt="Enter the support to push order ratio (e.g., 4.0 for 4:1 ratio) >>> ",
                  type_str="decimal",
                  validator=validate_ratio,
                  default=Decimal("4.0"),
                  prompt_on_new=True,
                  ),
    "max_price_increase_per_hour":
        ConfigVar(key="max_price_increase_per_hour",
                  prompt="Enter the maximum price increase per hour (%) >>> ",
                  type_str="decimal",
                  validator=validate_percentage,
                  default=Decimal("2.0"),
                  prompt_on_new=True,
                  ),
    "volume_threshold_multiplier":
        ConfigVar(key="volume_threshold_multiplier",
                  prompt="Enter the volume threshold multiplier (e.g., 2.0 for 2x normal volume) >>> ",
                  type_str="decimal",
                  validator=validate_multiplier,
                  default=Decimal("2.0"),
                  prompt_on_new=True,
                  ),
    "stabilization_time":
        ConfigVar(key="stabilization_time",
                  prompt="Enter the stabilization time per level in seconds >>> ",
                  type_str="float",
                  validator=validate_time_seconds,
                  default=300.0,
                  prompt_on_new=True,
                  ),
    "order_age_timeout":
        ConfigVar(key="order_age_timeout",
                  prompt="Enter the order age timeout in seconds (how long orders stay active) >>> ",
                  type_str="float",
                  validator=validate_time_seconds,
                  default=1800.0,  # 30 minutes
                  prompt_on_new=False,
                  ),
    # DEX Wallet Configuration
    "dex_wallet_mnemonic":
        ConfigVar(key="dex_wallet_mnemonic",
                  prompt="Enter DEX wallet mnemonic phrase (12 words, leave empty for simulation only) >>> ",
                  validator=validate_mnemonic,
                  default="",
                  prompt_on_new=False,
                  ),
    "dex_wallet_derivation_path":
        ConfigVar(key="dex_wallet_derivation_path",
                  prompt="Enter DEX wallet derivation path (default: m/44'/60'/0'/0/0) >>> ",
                  validator=validate_derivation_path,
                  default="m/44'/60'/0'/0/0",
                  prompt_on_new=False,
                  ),
    "dex_wallet_address":
        ConfigVar(key="dex_wallet_address",
                  prompt="Enter DEX wallet address (derived from private key if not provided) >>> ",
                  default="",
                  prompt_on_new=False,
                  ),
    "web3_rpc_url":
        ConfigVar(key="web3_rpc_url",
                  prompt="Enter Base network RPC URL (Alchemy recommended to avoid rate limits) >>> ",
                  default="https://base-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_API_KEY",
                  prompt_on_new=False,
                  ),
    "dex_enabled":
        ConfigVar(key="dex_enabled",
                  prompt="Enable actual DEX transactions? (Yes/No, No = simulation only) >>> ",
                  type_str="bool",
                  validator=validate_bool,
                  default=False,
                  prompt_on_new=False,
                  ),
    "min_weth_balance":
        ConfigVar(key="min_weth_balance",
                  prompt="Enter minimum WETH balance required for DEX operations >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("0.1"),
                  prompt_on_new=False,
                  ),
    "max_gas_price_gwei":
        ConfigVar(key="max_gas_price_gwei",
                  prompt="Enter maximum gas price in Gwei for DEX transactions >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("50"),
                  prompt_on_new=False,
                  ),
    "slippage_tolerance_pct":
        ConfigVar(key="slippage_tolerance_pct",
                  prompt="Enter slippage tolerance percentage for DEX swaps >>> ",
                  type_str="decimal",
                  validator=validate_percentage,
                  default=Decimal("2.0"),
                  prompt_on_new=False,
                  ),
    # Simulation Mode Settings
    "simulation_mode":
        ConfigVar(key="simulation_mode",
                  prompt="Enable simulation mode? (Yes/No, Yes = no real orders) >>> ",
                  type_str="bool",
                  validator=validate_bool,
                  default=True,
                  prompt_on_new=True,
                  ),
    "simulation_dex_weth_balance":
        ConfigVar(key="simulation_dex_weth_balance",
                  prompt="Enter simulation DEX WETH balance >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("1.0"),
                  prompt_on_new=False,
                  ),
    "simulation_mexc_usdt_balance":
        ConfigVar(key="simulation_mexc_usdt_balance",
                  prompt="Enter simulation MEXC USDT balance >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("5000.0"),
                  prompt_on_new=False,
                  ),
    "simulation_mexc_fual_balance":
        ConfigVar(key="simulation_mexc_fual_balance",
                  prompt="Enter simulation MEXC FUAL balance >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("1000000.0"),
                  prompt_on_new=False,
                  ),
    "simulation_dex_fual_balance":
        ConfigVar(key="simulation_dex_fual_balance",
                  prompt="Enter simulation DEX FUAL balance >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("500000.0"),
                  prompt_on_new=False,
                  ),
    # Arbitrage Parameters
    "max_arbitrage_tolerance_pct":
        ConfigVar(key="max_arbitrage_tolerance_pct",
                  prompt="Enter maximum arbitrage tolerance before strategy adjustment (%) >>> ",
                  type_str="decimal",
                  validator=validate_percentage,
                  default=Decimal("1.0"),
                  prompt_on_new=False,
                  ),
    "base_arbitrage_decay_factor":
        ConfigVar(key="base_arbitrage_decay_factor",
                  prompt="Enter base arbitrage decay factor (0.7 = 70% of price change gets arbitraged away) >>> ",
                  type_str="decimal",
                  validator=validate_ratio,
                  default=Decimal("0.7"),
                  prompt_on_new=False,
                  ),
    "coordination_enabled_dex":
        ConfigVar(key="coordination_enabled_dex",
                  prompt="Enable DEX coordination for arbitrage protection? (Yes/No) >>> ",
                  type_str="bool",
                  validator=validate_bool,
                  default=True,
                  prompt_on_new=False,
                  ),
    "min_net_price_impact":
        ConfigVar(key="min_net_price_impact",
                  prompt="Enter minimum net price impact required to proceed (%) >>> ",
                  type_str="decimal",
                  validator=validate_percentage,
                  default=Decimal("0.5"),
                  prompt_on_new=False,
                  ),
    "min_arbitrage_decay":
        ConfigVar(key="min_arbitrage_decay",
                  prompt="Enter minimum arbitrage decay (0.2 = 20% decay, max 80% arbitrage efficiency) >>> ",
                  type_str="decimal",
                  validator=validate_ratio,
                  default=Decimal("0.2"),
                  prompt_on_new=False,
                  ),
    "max_arbitrage_decay":
        ConfigVar(key="max_arbitrage_decay",
                  prompt="Enter maximum arbitrage decay (0.95 = 95% decay, min 5% arbitrage efficiency) >>> ",
                  type_str="decimal",
                  validator=validate_ratio,
                  default=Decimal("0.95"),
                  prompt_on_new=False,
                  ),
    "gas_cost_threshold":
        ConfigVar(key="gas_cost_threshold",
                  prompt="Enter gas cost threshold that significantly impacts arbitrage (USD) >>> ",
                  type_str="decimal",
                  validator=validate_budget,
                  default=Decimal("50.0"),
                  prompt_on_new=False,
                  ),
    "liquidity_ratio_threshold":
        ConfigVar(key="liquidity_ratio_threshold",
                  prompt="Enter critical liquidity ratio threshold for arbitrage calculations >>> ",
                  type_str="decimal",
                  validator=validate_ratio,
                  default=Decimal("0.1"),
                  prompt_on_new=False,
                  ),
    "volatility_threshold":
        ConfigVar(key="volatility_threshold",
                  prompt="Enter high volatility threshold for arbitrage adjustments (%) >>> ",
                  type_str="decimal",
                  validator=validate_percentage,
                  default=Decimal("5.0"),
                  prompt_on_new=False,
                  ),
}
