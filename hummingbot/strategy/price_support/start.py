from decimal import Decimal

from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.price_support import PriceSupport
from hummingbot.strategy.price_support.price_support_config_map import price_support_config_map as c_map

try:
    from eth_account import Account
    from mnemonic import Mnemonic
    MNEMONIC_AVAILABLE = True
except ImportError:
    MNEMONIC_AVAILABLE = False


def _derive_private_key_from_mnemonic(mnemonic_phrase: str, derivation_path: str = "m/44'/60'/0'/0/0") -> str:
    """
    Derive private key from mnemonic phrase using BIP44 derivation path.

    Args:
        mnemonic_phrase: 12-word mnemonic phrase
        derivation_path: BIP44 derivation path (default: m/44'/60'/0'/0/0)

    Returns:
        Private key as hex string (without 0x prefix)
    """
    if not mnemonic_phrase or not mnemonic_phrase.strip():
        return ""  # Return empty string for simulation mode

    if not MNEMONIC_AVAILABLE:
        raise ImportError("Required libraries not available. Install with: pip install mnemonic eth-account")

    try:
        # Validate mnemonic
        mnemo = Mnemonic("english")
        if not mnemo.check(mnemonic_phrase.strip()):
            raise ValueError("Invalid mnemonic phrase")

        # Derive account from mnemonic using derivation path
        Account.enable_unaudited_hdwallet_features()
        account = Account.from_mnemonic(mnemonic_phrase.strip(), account_path=derivation_path)

        # Return private key without 0x prefix
        return account.key.hex()[2:] if account.key.hex().startswith('0x') else account.key.hex()

    except Exception as e:
        raise ValueError(f"Failed to derive private key from mnemonic: {str(e)}")


def start(self):
    """
    Start the advanced price support strategy with comprehensive configuration
    """
    # Get basic configuration with null checking
    connector_var = c_map.get("connector")
    connector = connector_var.value.lower() if connector_var is not None and connector_var.value is not None else "mexc"

    market_var = c_map.get("market")
    market = market_var.value if market_var is not None and market_var.value is not None else "FULA-USDT"

    # Initialize markets
    self._initialize_markets([(connector, [market])])
    base, quote = market.split("-")
    market_info = MarketTradingPairTuple(self.markets[connector], market, base, quote)
    self.market_trading_pair_tuples = [market_info]

    # Get advanced configuration parameters with null checking
    target_price_pct_var = c_map.get("target_price_pct")
    target_price_pct = target_price_pct_var.value if target_price_pct_var is not None and target_price_pct_var.value is not None else Decimal("0")

    max_budget_var = c_map.get("max_budget")
    max_budget = max_budget_var.value if max_budget_var is not None and max_budget_var.value is not None else Decimal("1000")

    stop_loss_pct_var = c_map.get("stop_loss_pct")
    stop_loss_pct = stop_loss_pct_var.value if stop_loss_pct_var is not None and stop_loss_pct_var.value is not None else Decimal("5.0")

    uniswap_token_address_var = c_map.get("uniswap_token_address")
    uniswap_token_address = uniswap_token_address_var.value if uniswap_token_address_var is not None and uniswap_token_address_var.value is not None else "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB"

    analysis_interval_var = c_map.get("analysis_interval")
    analysis_interval = analysis_interval_var.value if analysis_interval_var is not None and analysis_interval_var.value is not None else 30.0

    coordination_enabled_var = c_map.get("coordination_enabled")
    coordination_enabled = coordination_enabled_var.value if coordination_enabled_var is not None and coordination_enabled_var.value is not None else True

    emergency_stop_enabled_var = c_map.get("emergency_stop_enabled")
    emergency_stop_enabled = emergency_stop_enabled_var.value if emergency_stop_enabled_var is not None and emergency_stop_enabled_var.value is not None else True

    max_daily_loss_var = c_map.get("max_daily_loss")
    max_daily_loss = max_daily_loss_var.value if max_daily_loss_var is not None and max_daily_loss_var.value is not None else Decimal("500")

    min_order_size_var = c_map.get("min_order_size")
    min_order_size = min_order_size_var.value if min_order_size_var is not None and min_order_size_var.value is not None else Decimal("10")

    max_order_size_var = c_map.get("max_order_size")
    max_order_size = max_order_size_var.value if max_order_size_var is not None and max_order_size_var.value is not None else Decimal("200")

    # Get enhanced strategy parameters with null checking
    support_layers_var = c_map.get("support_layers")
    support_layers = support_layers_var.value if support_layers_var is not None and support_layers_var.value is not None else 5

    support_to_push_ratio_var = c_map.get("support_to_push_ratio")
    support_to_push_ratio = support_to_push_ratio_var.value if support_to_push_ratio_var is not None and support_to_push_ratio_var.value is not None else Decimal("4.0")

    max_price_increase_per_hour_var = c_map.get("max_price_increase_per_hour")
    max_price_increase_per_hour = max_price_increase_per_hour_var.value if max_price_increase_per_hour_var is not None and max_price_increase_per_hour_var.value is not None else Decimal("0.5")

    volume_threshold_multiplier_var = c_map.get("volume_threshold_multiplier")
    volume_threshold_multiplier = volume_threshold_multiplier_var.value if volume_threshold_multiplier_var is not None and volume_threshold_multiplier_var.value is not None else Decimal("2.0")

    stabilization_time_var = c_map.get("stabilization_time")
    stabilization_time = stabilization_time_var.value if stabilization_time_var is not None and stabilization_time_var.value is not None else 300.0

    order_age_timeout_var = c_map.get("order_age_timeout")
    order_age_timeout = order_age_timeout_var.value if order_age_timeout_var is not None and order_age_timeout_var.value is not None else 1800.0

    # Get DEX wallet configuration with null checking
    dex_wallet_mnemonic_var = c_map.get("dex_wallet_mnemonic")
    dex_wallet_mnemonic = dex_wallet_mnemonic_var.value if dex_wallet_mnemonic_var is not None and dex_wallet_mnemonic_var.value is not None else ""

    dex_wallet_derivation_path_var = c_map.get("dex_wallet_derivation_path")
    dex_wallet_derivation_path = dex_wallet_derivation_path_var.value if dex_wallet_derivation_path_var is not None and dex_wallet_derivation_path_var.value is not None else "m/44'/60'/0'/0/0"

    dex_wallet_address_var = c_map.get("dex_wallet_address")
    dex_wallet_address = dex_wallet_address_var.value if dex_wallet_address_var is not None and dex_wallet_address_var.value is not None else ""

    web3_rpc_url_var = c_map.get("web3_rpc_url")
    web3_rpc_url = web3_rpc_url_var.value if web3_rpc_url_var is not None and web3_rpc_url_var.value is not None else "https://mainnet.infura.io/v3/YOUR_PROJECT_ID"

    dex_enabled_var = c_map.get("dex_enabled")
    dex_enabled = dex_enabled_var.value if dex_enabled_var is not None and dex_enabled_var.value is not None else False

    min_weth_balance_var = c_map.get("min_weth_balance")
    min_weth_balance = min_weth_balance_var.value if min_weth_balance_var is not None and min_weth_balance_var.value is not None else Decimal("0.1")

    max_gas_price_gwei_var = c_map.get("max_gas_price_gwei")
    max_gas_price_gwei = max_gas_price_gwei_var.value if max_gas_price_gwei_var is not None and max_gas_price_gwei_var.value is not None else Decimal("50")

    slippage_tolerance_pct_var = c_map.get("slippage_tolerance_pct")
    slippage_tolerance_pct = slippage_tolerance_pct_var.value if slippage_tolerance_pct_var is not None and slippage_tolerance_pct_var.value is not None else Decimal("2.0")

    # Note: Simulation mode and arbitrage parameters are loaded by the strategy internally
    # They don't need to be passed as constructor parameters

    # Create strategy with full configuration including enhanced parameters
    self.strategy = PriceSupport(
        market_info=market_info,
        target_price_pct=target_price_pct,
        max_budget=max_budget,
        stop_loss_pct=stop_loss_pct,
        uniswap_token_address=uniswap_token_address,
        analysis_interval=analysis_interval,
        coordination_enabled=coordination_enabled,
        emergency_stop_enabled=emergency_stop_enabled,
        max_daily_loss=max_daily_loss,
        min_order_size=min_order_size,
        max_order_size=max_order_size,
        # Enhanced strategy parameters
        support_layers=support_layers,
        support_to_push_ratio=support_to_push_ratio,
        max_price_increase_per_hour=max_price_increase_per_hour,
        volume_threshold_multiplier=volume_threshold_multiplier,
        stabilization_time=stabilization_time,
        order_age_timeout=order_age_timeout,
        # DEX wallet configuration
        dex_wallet_private_key=_derive_private_key_from_mnemonic(dex_wallet_mnemonic, dex_wallet_derivation_path),
        dex_wallet_mnemonic=dex_wallet_mnemonic,
        dex_wallet_derivation_path=dex_wallet_derivation_path,
        dex_wallet_address=dex_wallet_address,
        web3_rpc_url=web3_rpc_url,
        dex_enabled=dex_enabled,
        min_weth_balance=min_weth_balance,
        max_gas_price_gwei=max_gas_price_gwei,
        slippage_tolerance_pct=slippage_tolerance_pct
    )

    # Log strategy initialization
    self.logger().info("=" * 60)
    self.logger().info("ENHANCED PRICE SUPPORT STRATEGY INITIALIZED")
    self.logger().info("=" * 60)
    self.logger().info(f"Exchange: {connector.upper()}")
    self.logger().info(f"Trading Pair: {market}")
    self.logger().info(f"Target Price Increase: {target_price_pct}% (0 = auto-set to 5%)")
    self.logger().info(f"Max Budget: ${max_budget}")
    self.logger().info(f"Stop Loss: {stop_loss_pct:.1f}%")
    self.logger().info(f"Max Daily Loss: ${max_daily_loss}")
    self.logger().info(f"Order Size Range: ${min_order_size} - ${max_order_size}")
    self.logger().info(f"Analysis Interval: {analysis_interval}s")
    self.logger().info(f"Coordination Enabled: {coordination_enabled}")
    self.logger().info(f"Emergency Stop: {emergency_stop_enabled}")
    self.logger().info(f"Uniswap Token: {uniswap_token_address}")
    self.logger().info("")
    self.logger().info("ENHANCED STRATEGY PARAMETERS:")
    self.logger().info(f"Support Layers: {support_layers}")
    self.logger().info(f"Support:Push Ratio: {support_to_push_ratio}:1")
    self.logger().info(f"Max Price Increase/Hour: {max_price_increase_per_hour}%")
    self.logger().info(f"Volume Threshold: {volume_threshold_multiplier}x normal")
    self.logger().info(f"Stabilization Time: {stabilization_time / 60:.1f} minutes")
    self.logger().info("=" * 60)

    # Display enhanced strategy goals and warnings
    self.logger().info("ENHANCED STRATEGY GOALS:")
    self.logger().info("1. Build layered support levels below current price")
    self.logger().info("2. Push price gradually with time-based controls")
    self.logger().info("3. Wait for sufficient volume before price increases")
    self.logger().info("4. Maintain 4:1 ratio of support to push orders")
    self.logger().info("5. Stabilize each price level before next push")
    self.logger().info("6. Coordinate with pure market making strategy")
    self.logger().info("7. Implement strict stop-loss protection")
    self.logger().info("8. Provide comprehensive logging and monitoring")
    self.logger().info("")
    self.logger().info("ENHANCED FEATURES:")
    self.logger().info("✓ Sustainable price increases with proper support")
    self.logger().info("✓ Volume-aware timing prevents low-liquidity pushes")
    self.logger().info("✓ Time-based limits prevent excessive manipulation")
    self.logger().info("✓ Layered approach reduces retracement risk")
    self.logger().info("")
    self.logger().warning("⚠️  RISK WARNING:")
    self.logger().warning("This enhanced strategy involves significant financial risk.")
    self.logger().warning("Monitor performance closely and adjust parameters as needed.")
    self.logger().warning("Stop-loss protection is active but not guaranteed.")
    self.logger().warning("Enhanced controls reduce but do not eliminate risks.")
    self.logger().info("=" * 60)
