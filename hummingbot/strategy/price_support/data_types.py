from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import Dict, List, NamedTuple, Optional, Tuple

from hummingbot.core.data_type.common import OrderType


class SupportPhase(Enum):
    """Price support strategy phases"""
    ANALYSIS = "ANALYSIS"
    ACCUMULATION = "ACCUMULATION"
    SUPPORT = "SUPPORT"
    MAINTENANCE = "MAINTENANCE"
    EMERGENCY_STOP = "EMERGENCY_STOP"


class MarketCondition(Enum):
    """Market condition assessment"""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    NEUTRAL = "NEUTRAL"
    VOLATILE = "VOLATILE"
    ILLIQUID = "ILLIQUID"


@dataclass
class OrderBookLevel:
    """Order book level data"""
    price: Decimal
    size: Decimal
    cumulative_size: Decimal
    distance_from_mid: Decimal


@dataclass
class MarketAnalysis:
    """Market analysis results"""
    mexc_mid_price: Decimal
    uniswap_price: Decimal
    price_difference: Decimal
    arbitrage_opportunity: bool
    mexc_bid_depth: Decimal
    mexc_ask_depth: Decimal
    resistance_levels: List[Decimal]
    support_levels: List[Decimal]
    market_condition: MarketCondition
    volatility: Decimal
    volume_24h: Decimal
    timestamp: float


@dataclass
class BalanceAnalysis:
    """Trading balance analysis"""
    base_balance: Decimal
    quote_balance: Decimal
    total_usd_value: Decimal
    max_position_size: Decimal
    recommended_order_size: Decimal
    risk_budget: Decimal
    max_daily_loss: Decimal


@dataclass
class PriceTarget:
    """Price target calculation"""
    current_price: Decimal
    target_price: Decimal
    price_increase_pct: Decimal
    required_capital: Decimal
    estimated_time: float
    confidence: Decimal
    risk_level: str


@dataclass
class SupportOrder:
    """Support order specification"""
    price: Decimal
    size: Decimal
    order_type: OrderType
    priority: int
    reason: str
    max_age: float


class OrdersProposal(NamedTuple):
    """Orders proposal for execution"""
    actions: int
    buy_orders: List[SupportOrder]
    cancel_order_ids: List[str]
    total_buy_amount: Decimal
    estimated_impact: Decimal


class RiskMetrics(NamedTuple):
    """Risk assessment metrics"""
    current_loss: Decimal
    daily_loss: Decimal
    position_risk: Decimal
    market_risk: Decimal
    liquidity_risk: Decimal
    overall_risk_score: Decimal


@dataclass
class UniswapV3TickData:
    """Uniswap V3 tick data for concentrated liquidity analysis"""
    tick_index: int
    liquidity_gross: Decimal
    liquidity_net: Decimal
    price: Decimal
    fee_growth_outside_0: Decimal
    fee_growth_outside_1: Decimal
    initialized: bool


@dataclass
class UniswapV3PositionData:
    """Uniswap V3 position data for liquidity analysis"""
    position_id: int
    tick_lower: int
    tick_upper: int
    liquidity: Decimal
    fee_growth_inside_0_last: Decimal
    fee_growth_inside_1_last: Decimal
    tokens_owed_0: Decimal
    tokens_owed_1: Decimal


@dataclass
class AMMLiquidityAnalysis:
    """Comprehensive AMM liquidity analysis"""
    # Basic pool data
    pool_address: str
    token0_address: str
    token1_address: str
    token0_symbol: str
    token1_symbol: str
    fee_tier: Decimal

    # V2-style reserves (for compatibility and K constant)
    token0_reserve: Decimal
    token1_reserve: Decimal
    k_constant: Decimal  # x * y = k

    # V3-specific data
    sqrt_price_x96: Decimal
    current_tick: int
    liquidity: Decimal  # Current active liquidity
    tick_spacing: int

    # Concentrated liquidity analysis
    active_liquidity_range: Tuple[int, int]  # (tick_lower, tick_upper)
    liquidity_distribution: List[UniswapV3TickData]
    major_positions: List[UniswapV3PositionData]

    # Price impact calculations
    price_impact_1_percent: Decimal  # Price impact for 1% of reserves
    price_impact_5_percent: Decimal  # Price impact for 5% of reserves
    price_impact_10_percent: Decimal  # Price impact for 10% of reserves

    # Liquidity depth analysis
    liquidity_depth_usd: Decimal
    effective_liquidity_usd: Decimal  # Liquidity within 2% price range
    concentrated_liquidity_ratio: Decimal  # Active vs total liquidity

    # Market metrics
    volume_24h: Decimal
    fees_24h: Decimal
    tvl_usd: Decimal
    utilization_rate: Decimal  # Volume/TVL ratio

    # Price and routing
    current_price_token0: Decimal  # Price of token0 in token1
    current_price_token1: Decimal  # Price of token1 in token0
    price_usd_token0: Decimal
    price_usd_token1: Decimal

    timestamp: float


@dataclass
class MultiPoolRouting:
    """Multi-pool routing analysis for optimal execution"""
    primary_pool: AMMLiquidityAnalysis
    alternative_pools: List[AMMLiquidityAnalysis]
    optimal_route: List[str]  # Pool addresses in order
    route_price_impact: Decimal
    route_gas_cost: Decimal
    total_liquidity_available: Decimal
    execution_complexity: int  # Number of hops


@dataclass
class MEVThreatAnalysis:
    """MEV threat detection and analysis"""
    sandwich_attack_risk: Decimal  # 0-1 scale
    front_running_risk: Decimal  # 0-1 scale
    back_running_risk: Decimal  # 0-1 scale
    flashloan_attack_risk: Decimal  # 0-1 scale

    # Detection metrics
    mempool_congestion: Decimal  # Current mempool congestion level
    gas_price_volatility: Decimal  # Recent gas price volatility
    large_pending_transactions: int  # Number of large pending transactions
    mev_bot_activity_score: Decimal  # Detected MEV bot activity

    # Protection recommendations
    recommended_gas_price: Decimal  # Recommended gas price in Gwei
    recommended_gas_limit: int  # Recommended gas limit
    private_mempool_recommended: bool  # Whether to use private mempool
    transaction_splitting_recommended: bool  # Whether to split large transactions

    timestamp: float


@dataclass
class SandwichAttackDetection:
    """Sandwich attack detection data"""
    detected: bool
    confidence_score: Decimal  # 0-1 confidence level
    front_run_transaction: Optional[str]  # Transaction hash if detected
    back_run_transaction: Optional[str]  # Transaction hash if detected
    estimated_profit_extraction: Decimal  # Estimated MEV profit in USD
    protection_gas_premium: Decimal  # Additional gas needed for protection
    timestamp: float


@dataclass
class PrivateMempoolConfig:
    """Private mempool configuration"""
    enabled: bool
    provider: str  # "flashbots", "eden", "manifold", etc.
    endpoint_url: str
    bundle_target_block: int  # Target block for bundle inclusion
    max_bundle_size: int  # Maximum transactions per bundle
    min_priority_fee: Decimal  # Minimum priority fee in Gwei
    bundle_timeout_seconds: int


@dataclass
class FlashloanResistantOrder:
    """Flashloan-resistant order structure"""
    order_id: str
    split_into_parts: int  # Number of parts to split order
    time_delay_between_parts: float  # Seconds between parts
    randomized_timing: bool  # Whether to randomize timing
    max_slippage_per_part: Decimal  # Maximum slippage per part
    gas_price_strategy: str  # "aggressive", "moderate", "conservative"
    private_mempool_required: bool


@dataclass
class MEVProtectionStrategy:
    """Comprehensive MEV protection strategy"""
    strategy_type: str  # "private_mempool", "gas_auction", "time_delay", "split_orders"

    # Gas pricing strategy
    base_gas_price: Decimal
    priority_fee: Decimal
    max_fee_per_gas: Decimal
    gas_price_escalation_rate: Decimal  # Rate to increase gas if not included

    # Transaction timing
    submission_delay_ms: int  # Delay before submission
    randomized_delay: bool  # Whether to randomize delay
    max_retry_attempts: int

    # Order splitting
    split_large_orders: bool
    max_order_size_usd: Decimal  # Maximum size before splitting
    min_split_delay_seconds: float

    # Private mempool settings
    private_mempool_config: Optional[PrivateMempoolConfig]

    # Monitoring
    enable_sandwich_detection: bool
    enable_front_running_detection: bool
    enable_flashloan_monitoring: bool

    estimated_protection_cost_usd: Decimal
    estimated_success_rate: Decimal  # 0-1 probability of successful execution


@dataclass
class PriceFeedData:
    """Individual price feed data with validation metrics"""
    source: str  # "mexc", "uniswap", "chainlink", "coingecko", etc.
    price: Decimal
    timestamp: float
    latency_ms: float  # Time taken to fetch this price
    confidence_score: Decimal  # 0-1 confidence in price accuracy
    volume_24h: Optional[Decimal]  # 24h trading volume if available
    last_update_block: Optional[int]  # Block number for on-chain sources
    is_stale: bool  # Whether price is considered stale
    staleness_seconds: float  # How old the price is
    validation_status: str  # "valid", "stale", "outlier", "error"


@dataclass
class CrossExchangePriceAnalysis:
    """Comprehensive cross-exchange price analysis"""
    primary_cex_price: PriceFeedData  # MEXC price
    primary_dex_price: PriceFeedData  # Uniswap price
    oracle_prices: List[PriceFeedData]  # External oracle prices
    backup_cex_prices: List[PriceFeedData]  # Backup CEX prices
    backup_dex_prices: List[PriceFeedData]  # Backup DEX prices

    # Consensus analysis
    consensus_price: Decimal  # Weighted consensus price
    price_deviation_threshold: Decimal  # Maximum allowed deviation
    outlier_sources: List[str]  # Sources with outlier prices

    # Latency analysis
    total_latency_ms: float  # Total time for all price fetches
    max_latency_ms: float  # Highest individual latency
    latency_adjusted_prices: Dict[str, Decimal]  # Prices adjusted for latency

    # Staleness detection
    stale_sources: List[str]  # Sources with stale prices
    freshest_price_age_seconds: float  # Age of freshest price
    stalest_price_age_seconds: float  # Age of stalest price

    # Validation results
    validation_passed: bool  # Whether overall validation passed
    validation_errors: List[str]  # List of validation errors
    recommended_price: Decimal  # Final recommended price to use
    confidence_level: Decimal  # 0-1 confidence in recommended price

    timestamp: float


@dataclass
class PriceDiscoveryConfig:
    """Configuration for price discovery system"""
    # Price feed sources
    primary_cex_source: str  # "mexc"
    primary_dex_source: str  # "uniswap"
    oracle_sources: List[str]  # ["chainlink", "coingecko", "coinmarketcap"]
    backup_cex_sources: List[str]  # ["binance", "kucoin", "gate"]
    backup_dex_sources: List[str]  # ["sushiswap", "curve"]

    # Validation thresholds
    max_price_deviation_pct: Decimal  # Maximum allowed price deviation (e.g., 5%)
    max_staleness_seconds: float  # Maximum allowed price age (e.g., 60 seconds)
    max_latency_ms: float  # Maximum allowed fetch latency (e.g., 5000ms)
    min_confidence_score: Decimal  # Minimum confidence score (e.g., 0.7)

    # Consensus calculation
    consensus_method: str  # "weighted_average", "median", "trimmed_mean"
    outlier_detection_method: str  # "z_score", "iqr", "mad"
    outlier_threshold: Decimal  # Threshold for outlier detection

    # Latency compensation
    enable_latency_compensation: bool
    latency_compensation_method: str  # "linear", "exponential", "none"
    max_latency_compensation_pct: Decimal  # Max adjustment for latency

    # Update intervals
    price_update_interval_ms: float  # How often to update prices
    validation_interval_ms: float  # How often to run validation
    oracle_update_interval_ms: float  # How often to update oracle prices


@dataclass
class LatencyMeasurement:
    """Latency measurement for price feeds"""
    source: str
    request_timestamp: float
    response_timestamp: float
    latency_ms: float
    network_hops: Optional[int]  # Number of network hops if available
    geographic_region: Optional[str]  # Source geographic region


@dataclass
class PriceStalenessDetection:
    """Price staleness detection results"""
    source: str
    last_price_update: float
    current_timestamp: float
    staleness_seconds: float
    is_stale: bool
    staleness_severity: str  # "fresh", "slightly_stale", "stale", "very_stale"
    recommended_action: str  # "use", "use_with_caution", "discard", "fallback"


@dataclass
class OraclePriceValidation:
    """Oracle price validation results"""
    oracle_source: str
    oracle_price: Decimal
    market_prices: List[Decimal]  # Prices from market sources
    deviation_from_market: Decimal  # Percentage deviation
    validation_status: str  # "valid", "minor_deviation", "major_deviation", "error"
    confidence_adjustment: Decimal  # Adjustment to confidence based on validation


@dataclass
class BudgetComponent:
    """Individual budget component with detailed tracking"""
    component_name: str  # "mexc_orders", "dex_swaps", "arbitrage_protection", etc.
    base_amount: Decimal  # Base budget requirement
    adjustments: Dict[str, Decimal]  # Named adjustments (slippage, gas, mev, etc.)
    total_amount: Decimal  # Final amount after adjustments
    allocation_priority: int  # 1=highest, 5=lowest priority
    is_essential: bool  # Whether this component is required for strategy success
    partial_execution_viable: bool  # Whether partial execution is acceptable
    min_viable_amount: Decimal  # Minimum amount for viable execution
    max_beneficial_amount: Decimal  # Maximum amount with diminishing returns
    execution_dependencies: List[str]  # Other components this depends on
    risk_multiplier: Decimal  # Risk adjustment factor (1.0 = normal risk)


@dataclass
class BudgetAllocation:
    """Comprehensive budget allocation with dynamic rebalancing"""
    total_available_budget: Decimal
    total_required_budget: Decimal
    allocation_efficiency: Decimal  # 0-1 score of how well budget is allocated

    # Core components
    mexc_component: BudgetComponent
    dex_component: BudgetComponent
    arbitrage_component: BudgetComponent
    coordination_component: BudgetComponent

    # Execution scenarios
    full_execution_budget: Decimal  # Budget for full coordinated execution
    partial_execution_budget: Decimal  # Budget for partial execution
    fallback_execution_budget: Decimal  # Budget for fallback (MEXC-only)

    # Dynamic adjustments
    real_time_adjustments: Dict[str, Decimal]  # Live adjustments based on conditions
    rebalancing_opportunities: List[str]  # Identified rebalancing opportunities

    # Execution tracking
    allocated_amounts: Dict[str, Decimal]  # Actually allocated amounts
    reserved_amounts: Dict[str, Decimal]  # Reserved for future execution
    contingency_amounts: Dict[str, Decimal]  # Emergency reserves

    # Performance metrics
    allocation_confidence: Decimal  # 0-1 confidence in allocation accuracy
    execution_probability: Decimal  # 0-1 probability of successful execution
    cost_efficiency_score: Decimal  # 0-1 score of cost efficiency

    timestamp: float


@dataclass
class ExecutionScenario:
    """Different execution scenarios with budget requirements"""
    scenario_name: str  # "coordinated_full", "coordinated_partial", "mexc_only", etc.
    scenario_description: str
    required_components: List[str]  # Required budget components
    optional_components: List[str]  # Optional budget components
    total_budget_required: Decimal
    success_probability: Decimal  # 0-1 probability of success
    expected_price_impact: Decimal  # Expected price impact percentage
    execution_time_estimate: float  # Estimated execution time in seconds
    risk_level: str  # "low", "medium", "high"
    fallback_scenarios: List[str]  # Fallback options if this scenario fails


@dataclass
class DynamicBudgetAdjustment:
    """Real-time budget adjustment based on market conditions"""
    adjustment_type: str  # "market_volatility", "liquidity_change", "gas_spike", etc.
    trigger_condition: str  # What triggered this adjustment
    original_amount: Decimal
    adjusted_amount: Decimal
    adjustment_factor: Decimal
    confidence_impact: Decimal  # Impact on allocation confidence
    timestamp: float
    expiry_time: Optional[float]  # When this adjustment expires
    auto_revert: bool  # Whether to auto-revert when conditions change


@dataclass
class BudgetRebalancingPlan:
    """Plan for rebalancing budget allocation"""
    rebalancing_trigger: str  # What triggered the rebalancing
    current_allocation: Dict[str, Decimal]
    proposed_allocation: Dict[str, Decimal]
    rebalancing_actions: List[str]  # Specific actions to take
    expected_improvement: Decimal  # Expected improvement in efficiency
    execution_cost: Decimal  # Cost of executing the rebalancing
    risk_assessment: str  # Risk level of the rebalancing
    approval_required: bool  # Whether manual approval is needed


@dataclass
class PartialExecutionPlan:
    """Plan for handling partial execution scenarios"""
    execution_percentage: Decimal  # 0-1 percentage of full execution
    prioritized_components: List[str]  # Components in order of priority
    component_allocations: Dict[str, Decimal]  # Allocation per component
    expected_outcome: str  # Expected result of partial execution
    completion_strategy: str  # How to complete remaining execution
    risk_mitigation: List[str]  # Risk mitigation measures
    monitoring_requirements: List[str]  # What to monitor during execution


@dataclass
class BudgetValidationResult:
    """Result of budget allocation validation"""
    is_valid: bool
    validation_errors: List[str]
    validation_warnings: List[str]
    double_counting_detected: List[str]  # Detected double-counting issues
    missing_components: List[str]  # Missing required components
    over_allocated_components: List[str]  # Components with excessive allocation
    efficiency_score: Decimal  # 0-1 efficiency score
    recommended_adjustments: List[str]  # Recommended improvements


@dataclass
class CrossExchangePositionLimit:
    """Cross-exchange position limits for coordinated operations"""
    exchange_name: str  # "mexc", "uniswap", etc.
    asset_symbol: str  # "FUAL", "WETH", "USDT"
    max_position_usd: Decimal  # Maximum position size in USD
    max_position_percentage: Decimal  # Maximum percentage of total portfolio
    current_position_usd: Decimal  # Current position size in USD
    current_position_percentage: Decimal  # Current percentage of portfolio
    utilization_ratio: Decimal  # Current / Max position ratio
    limit_breach_threshold: Decimal  # Threshold for limit breach warning (e.g., 0.8)
    emergency_liquidation_threshold: Decimal  # Threshold for emergency liquidation (e.g., 0.95)
    last_updated: float  # Timestamp of last update


@dataclass
class CorrelationRiskMetrics:
    """Correlation risk assessment between exchanges and assets"""
    mexc_uniswap_price_correlation: Decimal  # Price correlation between MEXC and Uniswap
    mexc_uniswap_volume_correlation: Decimal  # Volume correlation between exchanges
    cross_exchange_basis_risk: Decimal  # Risk from price differences between exchanges
    temporal_correlation: Decimal  # Time-based correlation of price movements
    correlation_breakdown_risk: Decimal  # Risk of correlation breakdown during stress
    hedging_effectiveness: Decimal  # Effectiveness of cross-exchange hedging
    correlation_confidence: Decimal  # Confidence in correlation measurements
    lookback_period_days: int  # Period used for correlation calculation
    last_correlation_update: float  # Timestamp of last correlation update


@dataclass
class LiquidityRiskAssessment:
    """Comprehensive liquidity risk assessment"""
    exchange_name: str  # "mexc", "uniswap"
    asset_pair: str  # "FUAL/USDT", "FUAL/WETH"

    # Market depth metrics
    bid_depth_usd: Decimal  # Total bid depth in USD
    ask_depth_usd: Decimal  # Total ask depth in USD
    depth_imbalance_ratio: Decimal  # Bid/Ask depth ratio

    # Liquidity concentration
    top_5_levels_percentage: Decimal  # Percentage of liquidity in top 5 levels
    liquidity_concentration_risk: Decimal  # Risk from concentrated liquidity

    # Volume and turnover
    avg_daily_volume_usd: Decimal  # Average daily volume
    volume_volatility: Decimal  # Volatility of daily volumes
    turnover_ratio: Decimal  # Volume / Market Cap ratio

    # Slippage analysis
    slippage_1k_usd: Decimal  # Slippage for $1k trade
    slippage_10k_usd: Decimal  # Slippage for $10k trade
    slippage_100k_usd: Decimal  # Slippage for $100k trade

    # Liquidity stress testing
    stress_test_scenarios: Dict[str, Decimal]  # Slippage under different stress scenarios
    liquidity_resilience_score: Decimal  # 0-1 score of liquidity resilience

    # Risk scores
    overall_liquidity_risk: Decimal  # 0-1 overall liquidity risk score
    execution_risk: Decimal  # Risk of poor execution due to liquidity
    market_impact_risk: Decimal  # Risk of significant market impact

    timestamp: float


@dataclass
class EmergencyStopTrigger:
    """Emergency stop trigger conditions and actions"""
    trigger_id: str  # Unique identifier for the trigger
    trigger_type: str  # "position_limit", "correlation_breakdown", "liquidity_crisis", etc.
    trigger_condition: str  # Description of the condition that triggered stop
    severity_level: str  # "warning", "critical", "emergency"

    # Trigger metrics
    threshold_value: Decimal  # The threshold that was breached
    current_value: Decimal  # The current value that breached threshold
    breach_percentage: Decimal  # How much the threshold was breached by

    # Response actions
    immediate_actions: List[str]  # Actions to take immediately
    liquidation_required: bool  # Whether position liquidation is required
    coordination_halt: bool  # Whether to halt cross-exchange coordination

    # Recovery conditions
    recovery_threshold: Decimal  # Threshold for recovery
    recovery_actions: List[str]  # Actions to take during recovery
    auto_recovery_enabled: bool  # Whether automatic recovery is enabled

    # Timing
    trigger_timestamp: float
    estimated_recovery_time: Optional[float]
    max_stop_duration: float  # Maximum time to keep stop active


@dataclass
class RiskLimitConfiguration:
    """Configuration for risk limits across the strategy"""
    # Position limits
    max_total_position_usd: Decimal  # Maximum total position across all exchanges
    max_single_exchange_position_pct: Decimal  # Max position on single exchange (% of total)
    max_asset_concentration_pct: Decimal  # Max concentration in single asset

    # Correlation limits
    min_correlation_threshold: Decimal  # Minimum acceptable correlation
    max_basis_risk_pct: Decimal  # Maximum basis risk between exchanges
    correlation_lookback_days: int  # Days to use for correlation calculation

    # Liquidity limits
    max_market_impact_pct: Decimal  # Maximum acceptable market impact
    min_liquidity_depth_usd: Decimal  # Minimum required liquidity depth
    max_slippage_tolerance_pct: Decimal  # Maximum acceptable slippage

    # Emergency stop conditions
    emergency_stop_triggers: List[str]  # List of conditions that trigger emergency stop
    auto_liquidation_threshold: Decimal  # Threshold for automatic liquidation
    coordination_halt_threshold: Decimal  # Threshold for halting coordination

    # Recovery settings
    recovery_cooldown_minutes: float  # Cooldown period before recovery attempts
    gradual_recovery_enabled: bool  # Whether to enable gradual recovery
    recovery_validation_required: bool  # Whether manual validation is required


@dataclass
class CoordinationFailureDetection:
    """Detection and handling of coordination failures"""
    failure_type: str  # "timing_mismatch", "execution_failure", "price_divergence", etc.
    detection_timestamp: float

    # Failure metrics
    mexc_execution_status: str  # "success", "partial", "failed"
    dex_execution_status: str  # "success", "partial", "failed"
    timing_deviation_seconds: float  # Deviation from planned timing
    price_impact_deviation_pct: Decimal  # Deviation from expected price impact

    # Impact assessment
    financial_impact_usd: Decimal  # Estimated financial impact of failure
    strategy_effectiveness_impact: Decimal  # Impact on strategy effectiveness
    reputation_risk: str  # "low", "medium", "high"

    # Recovery actions
    recovery_strategy: str  # Strategy for recovering from failure
    manual_intervention_required: bool
    estimated_recovery_cost_usd: Decimal

    # Prevention measures
    prevention_recommendations: List[str]
    risk_mitigation_actions: List[str]


@dataclass
class RiskDashboardMetrics:
    """Comprehensive risk dashboard metrics"""
    # Overall risk scores
    overall_risk_score: Decimal  # 0-1 overall risk score
    position_risk_score: Decimal  # Position-related risk
    correlation_risk_score: Decimal  # Correlation-related risk
    liquidity_risk_score: Decimal  # Liquidity-related risk
    coordination_risk_score: Decimal  # Coordination-related risk

    # Risk trend analysis
    risk_trend_24h: str  # "improving", "stable", "deteriorating"
    risk_velocity: Decimal  # Rate of risk change
    risk_acceleration: Decimal  # Acceleration of risk change

    # Alert levels
    active_warnings: List[str]  # Current active warnings
    critical_alerts: List[str]  # Current critical alerts
    emergency_conditions: List[str]  # Current emergency conditions

    # Performance impact
    risk_adjusted_performance: Decimal  # Performance adjusted for risk
    risk_efficiency_ratio: Decimal  # Return per unit of risk

    timestamp: float


@dataclass
class CrossExchangeOrderBookCorrelation:
    """Cross-exchange order book correlation analysis"""
    mexc_uniswap_price_correlation: Decimal  # Price level correlation between exchanges
    mexc_uniswap_depth_correlation: Decimal  # Depth correlation at similar price levels
    order_flow_synchronization: Decimal  # How synchronized order flows are
    arbitrage_pressure_indicator: Decimal  # Pressure from arbitrage activity

    # Price level analysis
    common_support_levels: List[Decimal]  # Support levels present on both exchanges
    common_resistance_levels: List[Decimal]  # Resistance levels present on both exchanges
    price_level_divergence: Decimal  # Divergence in key price levels

    # Order flow patterns
    mexc_order_flow_direction: str  # "buying", "selling", "neutral"
    uniswap_swap_flow_direction: str  # "buying", "selling", "neutral"
    cross_exchange_flow_alignment: Decimal  # 0-1 alignment score

    # Timing analysis
    order_flow_lag_seconds: float  # Lag between exchange order flows
    price_discovery_leader: str  # "mexc", "uniswap", "simultaneous"
    arbitrage_response_time: float  # Time for arbitrage to respond to price differences

    correlation_confidence: Decimal  # Confidence in correlation measurements
    timestamp: float


@dataclass
class MarketMakerBehaviorPattern:
    """Market maker behavior pattern recognition"""
    market_maker_id: str  # Unique identifier for the market maker
    exchange_name: str  # "mexc", "uniswap"

    # Order characteristics
    typical_order_size_range: Tuple[Decimal, Decimal]  # Min, max typical order sizes
    order_placement_frequency: Decimal  # Orders per minute
    order_cancellation_rate: Decimal  # Percentage of orders cancelled

    # Positioning patterns
    spread_maintenance_behavior: str  # "tight", "wide", "adaptive"
    inventory_management_style: str  # "balanced", "directional", "passive"
    price_following_behavior: str  # "leading", "following", "neutral"

    # Response patterns
    response_to_large_orders: str  # "withdraw", "compete", "ignore"
    response_to_volatility: str  # "widen_spreads", "reduce_size", "maintain"
    response_to_news: str  # "fast", "slow", "neutral"

    # Strategy implications for price support
    competition_level: str  # "high", "medium", "low"
    cooperation_potential: str  # "high", "medium", "low"
    impact_on_price_push: str  # "helpful", "neutral", "hindering"

    # Performance metrics
    uptime_percentage: Decimal  # Percentage of time active
    quote_quality_score: Decimal  # 0-1 quality of quotes provided
    market_share_percentage: Decimal  # Share of total market liquidity

    confidence_score: Decimal  # Confidence in pattern recognition
    last_updated: float


@dataclass
class LiquidityMigrationEvent:
    """Liquidity migration detection between exchanges"""
    event_id: str  # Unique identifier for the migration event
    migration_type: str  # "mexc_to_uniswap", "uniswap_to_mexc", "bidirectional"

    # Migration metrics
    liquidity_amount_usd: Decimal  # Amount of liquidity that migrated
    migration_speed: str  # "fast", "gradual", "instant"
    migration_trigger: str  # "arbitrage", "news", "technical", "unknown"

    # Before/after analysis
    mexc_liquidity_before: Decimal  # MEXC liquidity before migration
    mexc_liquidity_after: Decimal  # MEXC liquidity after migration
    uniswap_liquidity_before: Decimal  # Uniswap liquidity before migration
    uniswap_liquidity_after: Decimal  # Uniswap liquidity after migration

    # Price impact
    price_impact_mexc: Decimal  # Price impact on MEXC
    price_impact_uniswap: Decimal  # Price impact on Uniswap
    arbitrage_opportunity_created: bool  # Whether migration created arbitrage

    # Strategy implications
    impact_on_price_support: str  # "positive", "negative", "neutral"
    recommended_response: str  # "follow", "counter", "wait", "ignore"
    urgency_level: str  # "high", "medium", "low"

    # Timing
    detection_timestamp: float
    migration_start_time: float
    migration_end_time: Optional[float]
    duration_seconds: Optional[float]


@dataclass
class WashTradingIndicator:
    """Wash trading identification and analysis"""
    indicator_id: str  # Unique identifier for the indicator
    exchange_name: str  # "mexc", "uniswap"

    # Detection metrics
    wash_trading_probability: Decimal  # 0-1 probability of wash trading
    suspicious_volume_percentage: Decimal  # Percentage of volume that appears suspicious
    artificial_activity_score: Decimal  # 0-1 score of artificial activity

    # Pattern analysis
    round_trip_patterns: int  # Number of detected round-trip patterns
    self_trading_indicators: int  # Number of self-trading indicators
    volume_price_disconnection: Decimal  # Disconnection between volume and price movement

    # Timing patterns
    suspicious_timing_patterns: List[str]  # Types of suspicious timing patterns
    coordinated_activity_score: Decimal  # Score indicating coordinated wash trading
    bot_activity_probability: Decimal  # Probability of bot-driven wash trading

    # Impact assessment
    impact_on_real_liquidity: Decimal  # Impact on genuine liquidity
    impact_on_price_discovery: Decimal  # Impact on legitimate price discovery
    impact_on_spreads: Decimal  # Impact on bid-ask spreads

    # Strategy implications
    reliability_of_signals: Decimal  # How reliable price signals are
    recommended_order_adjustment: str  # "increase_size", "decrease_size", "avoid_level", "normal"
    risk_level: str  # "high", "medium", "low"

    # Evidence
    evidence_strength: str  # "strong", "moderate", "weak"
    detection_methods: List[str]  # Methods used for detection
    false_positive_probability: Decimal  # Probability this is a false positive

    timestamp: float


@dataclass
class OrderBookIntelligenceAnalysis:
    """Comprehensive order book intelligence analysis"""
    # Cross-exchange correlation
    cross_exchange_correlation: CrossExchangeOrderBookCorrelation

    # Market maker analysis
    detected_market_makers: List[MarketMakerBehaviorPattern]
    market_maker_competition_level: str  # "high", "medium", "low"
    market_maker_cooperation_score: Decimal  # 0-1 score of cooperation potential

    # Liquidity migration
    recent_liquidity_migrations: List[LiquidityMigrationEvent]
    liquidity_stability_score: Decimal  # 0-1 score of liquidity stability
    migration_risk_level: str  # "high", "medium", "low"

    # Wash trading detection
    wash_trading_indicators: List[WashTradingIndicator]
    overall_wash_trading_risk: Decimal  # 0-1 overall wash trading risk
    signal_reliability_score: Decimal  # 0-1 reliability of price signals

    # Strategic recommendations for price support
    optimal_order_placement_strategy: str  # Strategy recommendation
    recommended_order_sizes: Dict[str, Decimal]  # Recommended sizes by price level
    timing_recommendations: List[str]  # Timing-based recommendations
    risk_mitigation_actions: List[str]  # Actions to mitigate identified risks

    # Confidence and quality
    overall_analysis_confidence: Decimal  # 0-1 confidence in analysis
    data_quality_score: Decimal  # 0-1 quality of underlying data
    analysis_completeness: Decimal  # 0-1 completeness of analysis

    timestamp: float


@dataclass
class OrderFlowAnalysis:
    """Real-time order flow analysis for price support optimization"""
    # Order flow direction
    mexc_net_order_flow: Decimal  # Net order flow on MEXC (positive = buying pressure)
    uniswap_net_swap_flow: Decimal  # Net swap flow on Uniswap (positive = buying pressure)
    cross_exchange_flow_imbalance: Decimal  # Imbalance between exchanges

    # Flow intensity
    mexc_order_intensity: Decimal  # Orders per minute on MEXC
    uniswap_swap_intensity: Decimal  # Swaps per minute on Uniswap
    overall_market_activity: str  # "high", "medium", "low"

    # Price impact prediction
    predicted_mexc_price_impact: Decimal  # Predicted price impact from current flow
    predicted_uniswap_price_impact: Decimal  # Predicted price impact from current flow
    arbitrage_pressure_buildup: Decimal  # Building arbitrage pressure

    # Optimal timing for price support
    optimal_entry_timing: str  # "immediate", "wait_for_lull", "wait_for_momentum"
    momentum_alignment_score: Decimal  # 0-1 score of momentum alignment
    resistance_level_proximity: Decimal  # Distance to next resistance level

    # Strategy adjustments
    recommended_order_size_multiplier: Decimal  # Multiplier for standard order sizes
    recommended_speed_adjustment: str  # "faster", "slower", "normal"
    coordination_timing_adjustment: str  # "advance", "delay", "normal"

    timestamp: float


@dataclass
class CrossExchangeExecutionQuality:
    """Cross-exchange execution quality metrics"""
    execution_id: str  # Unique identifier for the execution

    # MEXC execution metrics
    mexc_orders_placed: int  # Number of orders placed on MEXC
    mexc_orders_filled: int  # Number of orders filled on MEXC
    mexc_fill_rate: Decimal  # Percentage of orders filled
    mexc_average_fill_time: float  # Average time to fill in seconds
    mexc_slippage_actual: Decimal  # Actual slippage experienced
    mexc_slippage_expected: Decimal  # Expected slippage
    mexc_price_impact_actual: Decimal  # Actual price impact achieved
    mexc_price_impact_expected: Decimal  # Expected price impact

    # Uniswap execution metrics
    uniswap_swaps_attempted: int  # Number of swaps attempted
    uniswap_swaps_successful: int  # Number of successful swaps
    uniswap_success_rate: Decimal  # Percentage of successful swaps
    uniswap_average_execution_time: float  # Average execution time
    uniswap_gas_cost_actual: Decimal  # Actual gas cost in USD
    uniswap_gas_cost_expected: Decimal  # Expected gas cost in USD
    uniswap_slippage_actual: Decimal  # Actual slippage experienced
    uniswap_slippage_expected: Decimal  # Expected slippage

    # Cross-exchange coordination metrics
    coordination_timing_accuracy: Decimal  # How well timed the coordination was (0-1)
    execution_sequence_adherence: Decimal  # How well execution followed planned sequence (0-1)
    cross_exchange_price_sync: Decimal  # How synchronized prices remained (0-1)
    arbitrage_leakage: Decimal  # Amount of value lost to arbitrage

    # Overall quality scores
    overall_execution_quality: Decimal  # 0-1 overall quality score
    timing_quality: Decimal  # 0-1 timing quality score
    cost_efficiency: Decimal  # 0-1 cost efficiency score
    impact_efficiency: Decimal  # 0-1 price impact efficiency score

    # Execution context
    market_conditions: str  # "favorable", "neutral", "challenging"
    coordination_complexity: str  # "simple", "moderate", "complex"
    execution_strategy: str  # Strategy used for execution

    timestamp: float


@dataclass
class ArbitrageEffectivenessMetrics:
    """Arbitrage effectiveness measurement and analysis"""
    measurement_id: str  # Unique identifier for the measurement

    # Arbitrage opportunity metrics
    opportunities_detected: int  # Number of arbitrage opportunities detected
    opportunities_exploited: int  # Number of opportunities successfully exploited
    opportunity_exploitation_rate: Decimal  # Percentage of opportunities exploited
    average_opportunity_size: Decimal  # Average size of arbitrage opportunities
    largest_opportunity_missed: Decimal  # Largest opportunity that was missed

    # Speed and timing metrics
    detection_speed: float  # Average time to detect opportunities (seconds)
    execution_speed: float  # Average time to execute arbitrage (seconds)
    total_response_time: float  # Total time from detection to execution
    speed_vs_competition: str  # "faster", "similar", "slower" vs other arbitrageurs

    # Profitability metrics
    gross_arbitrage_profit: Decimal  # Total gross profit from arbitrage
    arbitrage_costs: Decimal  # Total costs (gas, fees, slippage)
    net_arbitrage_profit: Decimal  # Net profit after costs
    profit_margin_percentage: Decimal  # Profit margin as percentage
    roi_on_arbitrage_capital: Decimal  # Return on capital used for arbitrage

    # Effectiveness vs. price support
    arbitrage_impact_on_price_support: Decimal  # How arbitrage affected price support
    price_support_decay_rate: Decimal  # Rate at which price support decayed due to arbitrage
    arbitrage_resistance_effectiveness: Decimal  # How well strategy resisted arbitrage
    coordination_vs_arbitrage_balance: Decimal  # Balance between coordination and arbitrage

    # Market impact analysis
    arbitrage_volume_vs_market_volume: Decimal  # Arbitrage volume as % of total market volume
    price_convergence_time: float  # Time for prices to converge after arbitrage
    market_efficiency_improvement: Decimal  # How arbitrage improved market efficiency
    liquidity_provision_impact: Decimal  # Impact on overall market liquidity

    # Competition analysis
    competing_arbitrageurs_detected: int  # Number of competing arbitrageurs
    market_share_of_arbitrage: Decimal  # Our share of total arbitrage activity
    arbitrage_competition_intensity: str  # "low", "medium", "high"

    # Strategy adaptation metrics
    strategy_adjustments_made: int  # Number of strategy adjustments due to arbitrage
    adaptation_effectiveness: Decimal  # How effective adaptations were (0-1)
    learning_rate: Decimal  # Rate of strategy improvement over time

    timestamp: float


@dataclass
class CoordinationSuccessMetrics:
    """Coordination success rate tracking and analysis"""
    coordination_id: str  # Unique identifier for coordination attempt

    # Coordination attempt metrics
    coordination_attempts: int  # Total coordination attempts
    successful_coordinations: int  # Number of successful coordinations
    partial_coordinations: int  # Number of partially successful coordinations
    failed_coordinations: int  # Number of failed coordinations
    coordination_success_rate: Decimal  # Overall success rate percentage

    # Success criteria analysis
    timing_success_rate: Decimal  # Percentage of coordinations with good timing
    execution_success_rate: Decimal  # Percentage with successful execution
    impact_success_rate: Decimal  # Percentage achieving target impact
    cost_success_rate: Decimal  # Percentage staying within cost targets

    # Failure analysis
    timing_failures: int  # Failures due to timing issues
    execution_failures: int  # Failures due to execution problems
    market_condition_failures: int  # Failures due to adverse market conditions
    technical_failures: int  # Failures due to technical issues
    coordination_complexity_failures: int  # Failures due to complexity

    # Performance trends
    success_rate_trend_7d: str  # "improving", "stable", "declining"
    success_rate_trend_24h: str  # Short-term trend
    average_coordination_duration: float  # Average time for coordination
    coordination_efficiency_trend: str  # Efficiency trend over time

    # Market condition correlation
    success_rate_high_volatility: Decimal  # Success rate during high volatility
    success_rate_low_volatility: Decimal  # Success rate during low volatility
    success_rate_high_volume: Decimal  # Success rate during high volume
    success_rate_low_volume: Decimal  # Success rate during low volume

    # Coordination complexity analysis
    simple_coordination_success_rate: Decimal  # Success rate for simple coordinations
    moderate_coordination_success_rate: Decimal  # Success rate for moderate complexity
    complex_coordination_success_rate: Decimal  # Success rate for complex coordinations

    # Learning and improvement metrics
    coordination_learning_rate: Decimal  # Rate of improvement over time
    best_practices_identified: List[str]  # Identified best practices
    common_failure_patterns: List[str]  # Common patterns leading to failure

    timestamp: float


@dataclass
class RealVsExpectedImpactAnalysis:
    """Real vs. expected impact analysis for strategy performance"""
    analysis_id: str  # Unique identifier for the analysis

    # Price impact analysis
    expected_price_impact: Decimal  # Expected price impact percentage
    actual_price_impact: Decimal  # Actual price impact achieved
    price_impact_accuracy: Decimal  # Accuracy of price impact prediction (0-1)
    price_impact_variance: Decimal  # Variance between expected and actual
    price_impact_efficiency: Decimal  # Efficiency of price impact achievement

    # Volume impact analysis
    expected_volume_impact: Decimal  # Expected volume required
    actual_volume_used: Decimal  # Actual volume used
    volume_efficiency: Decimal  # Volume efficiency (expected/actual)
    volume_optimization_score: Decimal  # How well volume was optimized (0-1)

    # Cost analysis
    expected_total_cost: Decimal  # Expected total cost
    actual_total_cost: Decimal  # Actual total cost incurred
    cost_accuracy: Decimal  # Accuracy of cost prediction (0-1)
    cost_efficiency: Decimal  # Cost efficiency (expected/actual)
    cost_breakdown_accuracy: Dict[str, Decimal]  # Accuracy by cost component

    # Timing analysis
    expected_execution_time: float  # Expected time to execute
    actual_execution_time: float  # Actual time taken
    timing_accuracy: Decimal  # Accuracy of timing prediction (0-1)
    timing_efficiency: Decimal  # Timing efficiency score (0-1)

    # Market response analysis
    expected_market_response: str  # Expected market response type
    actual_market_response: str  # Actual market response observed
    market_response_prediction_accuracy: Decimal  # Accuracy of market response prediction
    unexpected_market_events: List[str]  # Unexpected events that occurred

    # Arbitrage impact analysis
    expected_arbitrage_decay: Decimal  # Expected decay due to arbitrage
    actual_arbitrage_decay: Decimal  # Actual decay experienced
    arbitrage_prediction_accuracy: Decimal  # Accuracy of arbitrage prediction
    arbitrage_resistance_effectiveness: Decimal  # How well strategy resisted arbitrage

    # Coordination effectiveness analysis
    expected_coordination_benefit: Decimal  # Expected benefit from coordination
    actual_coordination_benefit: Decimal  # Actual benefit achieved
    coordination_effectiveness: Decimal  # Effectiveness of coordination (0-1)
    coordination_overhead_vs_benefit: Decimal  # Overhead vs. benefit ratio

    # Prediction model performance
    model_accuracy_overall: Decimal  # Overall model accuracy (0-1)
    model_accuracy_by_component: Dict[str, Decimal]  # Accuracy by component
    model_improvement_suggestions: List[str]  # Suggestions for model improvement
    prediction_confidence_vs_accuracy: Decimal  # Confidence vs. actual accuracy correlation

    # Learning and adaptation metrics
    prediction_error_trend: str  # "improving", "stable", "worsening"
    model_calibration_quality: Decimal  # How well calibrated predictions are (0-1)
    adaptive_learning_rate: Decimal  # Rate of model improvement

    timestamp: float


@dataclass
class ComprehensivePerformanceMetrics:
    """Comprehensive performance tracking for coordinated operations"""
    # Cross-exchange execution quality
    execution_quality: CrossExchangeExecutionQuality

    # Arbitrage effectiveness
    arbitrage_effectiveness: ArbitrageEffectivenessMetrics

    # Coordination success tracking
    coordination_success: CoordinationSuccessMetrics

    # Real vs. expected analysis
    impact_analysis: RealVsExpectedImpactAnalysis

    # Overall performance scores
    overall_performance_score: Decimal  # 0-1 overall performance score
    execution_performance_score: Decimal  # 0-1 execution performance
    coordination_performance_score: Decimal  # 0-1 coordination performance
    prediction_performance_score: Decimal  # 0-1 prediction accuracy

    # Performance trends
    performance_trend_24h: str  # "improving", "stable", "declining"
    performance_trend_7d: str  # Weekly trend
    performance_volatility: Decimal  # Volatility of performance metrics

    # Benchmarking
    performance_vs_baseline: Decimal  # Performance vs. baseline strategy
    performance_vs_simple_strategy: Decimal  # Performance vs. simple approach
    performance_percentile: Decimal  # Performance percentile vs. historical

    # Resource utilization
    computational_efficiency: Decimal  # Computational resource efficiency
    capital_efficiency: Decimal  # Capital utilization efficiency
    time_efficiency: Decimal  # Time utilization efficiency

    # Risk-adjusted performance
    risk_adjusted_return: Decimal  # Return adjusted for risk taken
    sharpe_ratio: Decimal  # Risk-adjusted performance ratio
    maximum_drawdown: Decimal  # Maximum drawdown experienced

    timestamp: float


@dataclass
class PerformanceAlert:
    """Performance monitoring alerts and notifications"""
    alert_id: str  # Unique identifier for the alert
    alert_type: str  # "performance_degradation", "prediction_error", "coordination_failure"
    severity: str  # "low", "medium", "high", "critical"

    # Alert details
    metric_name: str  # Name of the metric that triggered alert
    threshold_value: Decimal  # Threshold that was breached
    actual_value: Decimal  # Actual value that triggered alert
    deviation_percentage: Decimal  # How much the value deviated

    # Context information
    market_conditions: str  # Market conditions when alert triggered
    recent_strategy_changes: List[str]  # Recent changes that might be relevant
    potential_causes: List[str]  # Potential causes of the performance issue

    # Recommended actions
    immediate_actions: List[str]  # Actions to take immediately
    investigation_steps: List[str]  # Steps to investigate the issue
    prevention_measures: List[str]  # Measures to prevent recurrence

    # Alert metadata
    alert_timestamp: float
    resolution_timestamp: Optional[float]
    resolution_actions: List[str]
    alert_status: str  # "active", "investigating", "resolved"


@dataclass
class UniswapPoolData:
    """Legacy Uniswap pool data - maintained for compatibility"""
    token0_address: str
    token1_address: str
    token0_symbol: str
    token1_symbol: str
    token0_reserve: Decimal
    token1_reserve: Decimal
    total_liquidity_usd: Decimal
    price_token0: Decimal  # Price of token0 in token1
    price_token1: Decimal  # Price of token1 in token0
    fee_tier: Decimal
    volume_24h: Decimal
    timestamp: float


@dataclass
class ArbitrageAnalysis:
    """Cross-exchange arbitrage analysis"""
    mexc_price: Decimal
    uniswap_price: Decimal
    price_difference_pct: Decimal
    arbitrage_opportunity: bool
    arbitrage_direction: str  # "MEXC_TO_UNISWAP" or "UNISWAP_TO_MEXC"
    potential_profit_pct: Decimal
    required_volume_for_equilibrium: Decimal
    estimated_arbitrage_time: float
    liquidity_depth_ratio: Decimal  # MEXC depth / Uniswap depth


@dataclass
class CrossExchangeImpact:
    """Analysis of how price changes on one exchange affect the other"""
    mexc_price_change_pct: Decimal
    estimated_uniswap_price_change_pct: Decimal
    estimated_arbitrage_volume: Decimal
    net_price_impact_pct: Decimal  # Final price change after arbitrage
    arbitrage_decay_factor: Decimal  # How much of MEXC price change gets arbitraged away
    time_to_equilibrium: float
    required_mexc_volume: Decimal  # Volume needed on MEXC to achieve target net impact


@dataclass
class CoordinatedOrderPlan:
    """Plan for coordinated orders across MEXC and DEX"""
    mexc_orders: List[SupportOrder]
    dex_support_needed: bool
    estimated_net_price_impact: Decimal
    total_budget_required: Decimal
    mexc_budget: Decimal
    dex_budget: Decimal
    execution_sequence: List[str]  # Order of execution
    risk_assessment: str
