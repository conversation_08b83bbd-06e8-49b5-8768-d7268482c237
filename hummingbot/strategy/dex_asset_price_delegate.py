#!/usr/bin/env python
"""
DEX Asset Price Delegate for Uniswap V3 Integration

This module provides real-time price feeds from Uniswap V3 pools on Base network
for cross-platform coordination with MEXC market making strategy.

Key Features:
- Real-time Uniswap V3 price monitoring
- Pool liquidity analysis
- Arbitrage opportunity detection
- Price staleness detection
- Automatic failover to backup price sources
"""

import asyncio
import logging
import time
from decimal import Decimal
from typing import Dict, Optional

import aiohttp

from hummingbot.core.data_type.common import PriceType
from hummingbot.core.utils.async_utils import safe_ensure_future
from hummingbot.logger import HummingbotLogger
from hummingbot.strategy.asset_price_delegate import AssetPriceDelegate

# The Graph API endpoint for Uniswap V3 on Base
# Note: Base uses a different subgraph endpoint
# Updated to working endpoint (December 2024)
UNISWAP_V3_BASE_SUBGRAPH_URL = "https://gateway.thegraph.com/api/subgraphs/id/FUbEPQw1oMghy39fwWBFY5fE6MXPXZQtjncQy2cXdrNS"

# Backup price sources
COINGECKO_API_URL = "https://api.coingecko.com/api/v3/simple/price"
COINMARKETCAP_API_URL = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"


class DexAssetPriceDelegate(AssetPriceDelegate):
    """
    Asset price delegate that fetches prices from Uniswap V3 DEX on Base network
    with automatic failover to backup price sources.
    """

    _logger: Optional[HummingbotLogger] = None

    def __init__(self,
                 market,
                 base_token_address: str,
                 quote_token_address: str = "******************************************",  # Native ETH on Base
                 pool_fee_tier: int = 3000,  # 0.3% fee tier
                 update_interval: float = 30.0,  # Increased for rate limiting
                 staleness_threshold: float = 90.0,  # Increased for rate limiting
                 pool_address: Optional[str] = None,
                 bearer_token: Optional[str] = None,  # Bearer token for authenticated access
                 rate_limit_seconds: int = 30):  # Rate limit for subgraph requests
        """
        Initialize DEX price delegate

        :param market: The market connector
        :param base_token_address: Address of the base token (e.g., FULA)
        :param quote_token_address: Address of the quote token (default: WETH on Base)
        :param pool_fee_tier: Uniswap V3 pool fee tier (default: 3000 = 0.3%)
        :param update_interval: Price update interval in seconds
        :param staleness_threshold: Consider price stale after this many seconds
        :param pool_address: Specific pool address if known
        :param bearer_token: Bearer token for authenticated subgraph access
        :param rate_limit_seconds: Rate limit for subgraph requests
        """
        super().__init__()
        self._market = market
        self._base_token_address = base_token_address.lower()
        self._quote_token_address = quote_token_address.lower()
        self._pool_fee_tier = pool_fee_tier
        self._update_interval = max(update_interval, rate_limit_seconds)  # Respect rate limit
        self._staleness_threshold = staleness_threshold
        self._pool_address = pool_address.lower() if pool_address else None
        self._bearer_token = bearer_token
        self._rate_limit_seconds = rate_limit_seconds
        self._last_request_time = 0  # Track last subgraph request for rate limiting

        # Price data
        self._current_price: Optional[Decimal] = None
        self._last_update_time: float = 0
        self._pool_data: Dict = {}
        self._fula_token_tvl_usd: Decimal = Decimal("0")
        self._is_ready: bool = False

        # Async components
        self._session: Optional[aiohttp.ClientSession] = None
        self._update_task: Optional[asyncio.Task] = None
        self._ev_loop = asyncio.get_event_loop()
        self._should_stop: bool = False

        # Start price monitoring
        self.start()

    @classmethod
    def logger(cls) -> HummingbotLogger:
        if cls._logger is None:
            cls._logger = logging.getLogger(__name__)
        return cls._logger

    def start(self):
        """Start the price monitoring task"""
        if self._update_task is None or self._update_task.done():
            self._update_task = safe_ensure_future(self._price_update_loop())

    def stop(self):
        """Stop the price monitoring task"""
        self.logger().info("DEX_PRICE_DELEGATE: Stop requested")
        self._should_stop = True

        if self._update_task and not self._update_task.done():
            self.logger().info("DEX_PRICE_DELEGATE: Cancelling update task")
            self._update_task.cancel()
            # Schedule the async cleanup
            safe_ensure_future(self._async_stop())
        else:
            self.logger().info("DEX_PRICE_DELEGATE: No active update task to cancel")
            # Just close the session if there's no task
            if self._session and not self._session.closed:
                safe_ensure_future(self._session.close())

    async def _async_stop(self):
        """Async cleanup method"""
        try:
            # Wait for the task to be cancelled
            if self._update_task and not self._update_task.done():
                try:
                    await self._update_task
                except asyncio.CancelledError:
                    pass  # Expected when task is cancelled
                except Exception as e:
                    self.logger().error(f"DEX_PRICE_DELEGATE: Error during task cleanup: {str(e)}")

            # Close the session
            if self._session and not self._session.closed:
                await self._session.close()

            self.logger().info("DEX_PRICE_DELEGATE: Successfully stopped and cleaned up")
        except Exception as e:
            self.logger().error(f"DEX_PRICE_DELEGATE: Error during async stop: {str(e)}")

    async def _price_update_loop(self):
        """Main price update loop"""
        while not self._should_stop:
            try:
                await self._update_price_data()
                await asyncio.sleep(self._update_interval)
            except asyncio.CancelledError:
                self.logger().info("DEX_PRICE_DELEGATE: Price update loop cancelled")
                break
            except Exception as e:
                self.logger().error(f"DEX_PRICE_DELEGATE: Error in price update loop: {str(e)}")
                if not self._should_stop:
                    await asyncio.sleep(self._update_interval)

        self.logger().info("DEX_PRICE_DELEGATE: Price update loop stopped")

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=10)
            self._session = aiohttp.ClientSession(timeout=timeout)
        return self._session

    async def _update_price_data(self):
        """Update price data from Uniswap V3"""
        # Check if we should stop before doing any work
        if self._should_stop:
            self.logger().info("DEX_PRICE_DELEGATE: Skipping price update due to stop request")
            return

        try:
            # Try to get price from Uniswap V3 first
            price = await self._fetch_uniswap_price()

            # Check again after async operation
            if self._should_stop:
                self.logger().info("DEX_PRICE_DELEGATE: Stopping price update after fetch")
                return

            if price is not None:
                self._current_price = price
                self._last_update_time = time.time()
                self._is_ready = True
                self.logger().debug(f"DEX_PRICE_DELEGATE: Updated price to {price}")
            else:
                # Fallback to backup price sources
                price = await self._fetch_backup_price()
                if price is not None:
                    self._current_price = price
                    self._last_update_time = time.time()
                    self._is_ready = True
                    self.logger().warning(f"DEX_PRICE_DELEGATE: Using backup price: {price}")
                else:
                    self.logger().error("DEX_PRICE_DELEGATE: Failed to fetch price from all sources")

        except Exception as e:
            self.logger().error(f"DEX_PRICE_DELEGATE: Error updating price data: {str(e)}")

    async def _fetch_uniswap_price(self) -> Optional[Decimal]:
        """Fetch price from Uniswap V3 using authenticated subgraph API"""
        # Early exit if stop requested
        if self._should_stop:
            self.logger().info("DEX_PRICE_DELEGATE: Skipping Uniswap price fetch due to stop request")
            return None

        # Check if Bearer token is available
        if not self._bearer_token:
            self.logger().error("DEX_PRICE_DELEGATE: No Bearer token provided for authenticated subgraph access")
            return None

        # Rate limiting: check if enough time has passed since last request
        current_time = time.time()
        if current_time - self._last_request_time < self._rate_limit_seconds:
            time_to_wait = self._rate_limit_seconds - (current_time - self._last_request_time)
            self.logger().debug(f"DEX_PRICE_DELEGATE: Rate limited, need to wait {time_to_wait:.1f} seconds")
            return None

        try:
            session = await self._get_session()

            # Check again after async operation
            if self._should_stop:
                return None

            # Use the exact query format that works with authentication
            base_addr = self._base_token_address.lower()

            self.logger().info(f"DEX_PRICE_DELEGATE: Fetching price for token {base_addr}")

            # Use the exact query format from the working test - include _totalValueLockedUSD for FULA token value
            query_data = {
                "query": f'{{ tokens(first: 1, where: {{id:"{base_addr}"}}) {{ id lastPriceUSD _lastPricePool _totalValueLockedUSD }} }}',
                "operationName": "Subgraphs",
                "variables": {}
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._bearer_token}"
            }

            # Update last request time
            self._last_request_time = current_time

            async with session.post(
                UNISWAP_V3_BASE_SUBGRAPH_URL,  # Use the working authenticated subgraph URL
                json=query_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens = data.get("data", {}).get("tokens", [])

                    if tokens:
                        token = tokens[0]

                        # Log token data for user reference
                        self.logger().info(f"DEX_PRICE_DELEGATE: Found token {token['id']}")
                        self.logger().info(f"DEX_PRICE_DELEGATE: Last price USD: ${token['lastPriceUSD']}")
                        self.logger().info(f"DEX_PRICE_DELEGATE: Last price pool: {token['_lastPricePool']}")
                        self.logger().info(f"DEX_PRICE_DELEGATE: FULA token TVL: ${token.get('_totalValueLockedUSD', '0')}")

                        # Store pool address for reference
                        if not self._pool_address:
                            self._pool_address = token['_lastPricePool']

                        # Store FULA token value for reference
                        self._fula_token_tvl_usd = Decimal(token.get("_totalValueLockedUSD", "0"))

                        # Get price directly from lastPriceUSD
                        price = Decimal(token["lastPriceUSD"])

                        # Now fetch pool liquidity data
                        await self._fetch_pool_data(session, headers)

                        self.logger().debug(f"DEX_PRICE_DELEGATE: Token price: ${price}")
                        return price
                    else:
                        self.logger().warning(f"DEX_PRICE_DELEGATE: Token {base_addr} not found in subgraph")
                        return None
                else:
                    self.logger().error(f"DEX_PRICE_DELEGATE: GraphQL request failed with status {response.status}")
                    return None

        except Exception as e:
            self.logger().error(f"DEX_PRICE_DELEGATE: Error fetching Uniswap price: {str(e)}")
            return None

    async def _fetch_eth_price_usd(self) -> Optional[Decimal]:
        """Fetch ETH price in USD from CoinGecko"""
        try:
            session = await self._get_session()
            url = f"{COINGECKO_API_URL}?ids=ethereum&vs_currencies=usd"

            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    eth_price = data.get("ethereum", {}).get("usd")
                    if eth_price:
                        return Decimal(str(eth_price))
                return None
        except Exception as e:
            self.logger().error(f"DEX_PRICE_DELEGATE: Error fetching ETH price: {str(e)}")
            return None

    async def _fetch_pool_data(self, session: aiohttp.ClientSession, headers: Dict[str, str]):
        """Fetch pool liquidity data from Uniswap V3 subgraph"""
        try:
            # Early exit if stop requested
            if self._should_stop:
                self.logger().info("DEX_PRICE_DELEGATE: Skipping pool data fetch due to stop request")
                return

            # Only fetch pool data if we have a pool address
            if not self._pool_address:
                self.logger().warning("DEX_PRICE_DELEGATE: No pool address available for liquidity query")
                return

            pool_addr = self._pool_address.lower()
            self.logger().info(f"DEX_PRICE_DELEGATE: Fetching pool data for {pool_addr}")

            # Query pool data using the specific pool address - use liquidityPools as per your working example
            pool_query_data = {
                "query": f'{{ liquidityPools(first:1, where:{{id:"{pool_addr}"}}) {{ totalValueLockedUSD }} }}',
                "operationName": "Subgraphs",
                "variables": {}
            }

            async with session.post(
                UNISWAP_V3_BASE_SUBGRAPH_URL,
                json=pool_query_data,
                headers=headers
            ) as response:
                # Check again after async operation
                if self._should_stop:
                    self.logger().info("DEX_PRICE_DELEGATE: Stopping pool data fetch after request")
                    return

                if response.status == 200:
                    data = await response.json()
                    self.logger().debug(f"DEX_PRICE_DELEGATE: Pool query response: {data}")

                    if data is None:
                        self.logger().error("DEX_PRICE_DELEGATE: Received None response from pool query")
                        return

                    data_section = data.get("data")
                    if data_section is None:
                        self.logger().error(f"DEX_PRICE_DELEGATE: No 'data' section in response: {data}")
                        return

                    pools = data_section.get("liquidityPools", [])

                    if pools and len(pools) > 0:
                        pool = pools[0]

                        if pool is None:
                            self.logger().error("DEX_PRICE_DELEGATE: Pool data is None")
                            return

                        # Store pool data for liquidity calculations
                        self._pool_data = {
                            "totalValueLockedUSD": pool.get("totalValueLockedUSD", "0") if pool else "0"
                        }

                        self.logger().info(f"DEX_PRICE_DELEGATE: Pool TVL (WETH+FULA): ${pool.get('totalValueLockedUSD', '0')}")
                    else:
                        self.logger().warning(f"DEX_PRICE_DELEGATE: Pool {pool_addr} not found in subgraph")
                else:
                    self.logger().error(f"DEX_PRICE_DELEGATE: Pool data request failed with status {response.status}")
                    response_text = await response.text()
                    self.logger().error(f"DEX_PRICE_DELEGATE: Response body: {response_text}")

        except Exception as e:
            import traceback
            self.logger().error(f"DEX_PRICE_DELEGATE: Error fetching pool data: {str(e)}")
            self.logger().error(f"DEX_PRICE_DELEGATE: Traceback: {traceback.format_exc()}")

    async def _fetch_backup_price(self) -> Optional[Decimal]:
        """Fetch price from backup sources (CoinGecko, etc.)"""
        # This would implement backup price fetching logic
        # For now, return None to indicate no backup price available
        return None

    def get_price_by_type(self, price_type: PriceType) -> Decimal:
        """Get price by type (always returns mid price for DEX)"""
        return self.c_get_mid_price()

    def c_get_mid_price(self) -> Decimal:
        """Get the current mid price"""
        if self._current_price is None:
            return Decimal("nan")

        # Check if price is stale
        if time.time() - self._last_update_time > self._staleness_threshold:
            self.logger().warning("DEX_PRICE_DELEGATE: Price data is stale")
            return Decimal("nan")

        return self._current_price

    @property
    def ready(self) -> bool:
        """Check if the price delegate is ready"""
        return (self._is_ready and
                self._current_price is not None and
                time.time() - self._last_update_time < self._staleness_threshold)

    @property
    def market(self):
        """Get the associated market"""
        return self._market

    @property
    def pool_data(self) -> Dict:
        """Get the current pool data"""
        return self._pool_data.copy()

    @property
    def liquidity_usd(self) -> Decimal:
        """Get pool liquidity in USD"""
        if self._pool_data:
            tvl = self._pool_data.get("totalValueLockedUSD", "0")
            return Decimal(tvl)
        return Decimal("0")

    @property
    def volume_24h_usd(self) -> Decimal:
        """Get 24h volume in USD"""
        if self._pool_data:
            volume = self._pool_data.get("volumeUSD", "0")
            return Decimal(volume)
        return Decimal("0")
