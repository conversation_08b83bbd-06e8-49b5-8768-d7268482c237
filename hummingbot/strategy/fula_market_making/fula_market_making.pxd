# distutils: language=c++

from libc.stdint cimport int64_t

from hummingbot.strategy.strategy_base cimport StrategyBase


cdef class FulaMarketMakingStrategy(StrategyBase):
    cdef:
        # Market info and configuration
        object _market_info
        object _sb_order_tracker
        list _sb_markets
        dict _market_info_to_active_orders

        # Order parameters
        object _bid_spread
        object _ask_spread
        object _minimum_spread
        object _order_amount
        int _order_levels
        int _buy_levels
        int _sell_levels
        object _split_order_levels_enabled
        object _bid_order_level_spreads
        object _ask_order_level_spreads
        object _bid_order_level_amounts
        object _ask_order_level_amounts
        object _order_level_spread
        object _order_level_amount

        # Order timing and refresh
        double _order_refresh_time
        double _order_refresh_time_min
        double _order_refresh_time_max
        double _max_order_age
        object _order_refresh_tolerance_pct
        double _filled_order_delay
        double _cancel_timestamp
        double _create_timestamp
        double _last_timestamp
        double _last_order_creation_timestamp
        bint _should_create_orders
        object _limit_order_type
        bint _all_markets_ready
        bint _should_wait_order_cancel_confirmation

        # Inventory management
        bint _inventory_skew_enabled
        object _inventory_target_base_pct
        object _inventory_range_multiplier
        int _filled_buys_balance
        int _filled_sells_balance

        # Hanging orders
        bint _hanging_orders_enabled
        object _hanging_orders_tracker
        object _hanging_orders_cancel_pct

        # Order optimization and costs
        bint _order_optimization_enabled
        object _ask_order_optimization_depth
        object _bid_order_optimization_depth
        bint _add_transaction_costs_to_orders

        # Price sources and types
        object _asset_price_delegate
        object _inventory_cost_price_delegate
        object _price_type
        bint _take_if_crossed
        object _price_ceiling
        object _price_floor

        # Price band related
        object _price_band
        object _moving_price_band
        double _price_band_refresh_time
        double _price_band_refresh_timestamp
        int _price_band_refresh_count

        # Bot attack detection
        object _price_history
        double _last_attack_check
        bint _in_attack_mode
        double _attack_detection_threshold
        double _attack_detection_window

        # Price tracking and thresholds
        object _buy_prices
        object _sell_prices
        double _last_avg_update
        object _upper_threshold
        object _lower_threshold
        object _sell_wall_spread  # 0.05% below upper threshold

        # Price ratio configuration and spread adjustment
        bint _enable_spread_adjustment
        object _spread_adjustment_increment
        object _max_buy_sell_ratio
        object _max_price_ratio
        object _spread_adjustment_factor
        double _last_ratio_warning
        double _last_ratio_log
        double _ratio_warning_interval
        object _original_bid_spread
        object _original_ask_spread
        object _min_allowed_spread
        object _price_ratio_log_level

        # Order tracking and history
        dict _order_tracker
        object _order_history
        double _last_order_check

        # Startup and balance tracking
        bint _startup_complete
        double _startup_timestamp
        double _startup_delay
        int _balance_retry_count
        int _max_balance_retries
        double _balance_retry_delay

        # UI and notifications
        bint _ping_pong_enabled
        list _ping_pong_warning_lines
        bint _hb_app_notification
        object _order_override

        # Logging and monitoring
        int64_t _logging_options
        object _last_own_trade_price
        double _status_report_interval

        # PRODUCTION SAFETY: Buy-Low/Sell-High Enforcement System
        bint _enforce_buy_low_sell_high
        object _min_profit_margin
        object _price_trend_threshold
        double _price_memory_window
        object _recent_price_history
        object _current_price_trend
        double _trend_confidence
        object _safe_buy_ceiling
        object _safe_sell_floor
        double _last_boundary_update
        double _boundary_update_interval
        double _last_trend_analysis
        int _rejected_buy_orders
        int _rejected_sell_orders
        double _last_rejection_log
        double _rejection_log_interval

        # ANTI-DRAIN PROTECTION: For own-token market making
        bint _enable_anti_drain_protection
        object _max_order_imbalance_ratio
        object _max_volume_per_minute
        object _suspicious_volume_threshold
        int _rapid_trade_window
        int _max_trades_per_window
        int _volume_tracking_window
        object _recent_trades
        object _volume_history
        double _last_volume_reset
        bint _attack_detected
        double _attack_start_time
        object _attack_start_price
        object _attack_phase
        object _pump_peak_price
        double _attack_cooldown
        object _price_manipulation_threshold
        object _pump_threshold
        object _dump_threshold
        object _liquidity_scaling_factor
        object _max_buy_distance_from_attack_start

        # ENHANCED LIQUIDITY MONITORING
        object _usd_volume_tracking
        object _large_order_threshold
        object _balance_drain_threshold
        object _initial_base_balance
        object _initial_quote_balance
        object _alert_level
        object _max_balance_drain_pct
        object _large_order_history
        double _last_balance_check
        object _cumulative_buy_volume_usd
        object _cumulative_sell_volume_usd

        # ADVERSE SELECTION PROTECTION: Real-time volatility adaptation
        object _volatility_1m
        object _volatility_5m
        object _volatility_15m
        object _volatility_regime
        object _price_changes_1m
        object _price_changes_5m
        object _price_changes_15m
        double _last_volatility_update
        object _base_spread_multiplier
        object _min_spread_multiplier
        object _max_spread_multiplier

        # ADVERSE SELECTION PROTECTION: Momentum detection
        object _momentum_indicator
        object _price_velocity
        object _volume_momentum
        object _directional_pressure
        object _recent_fills
        object _aggressive_fill_ratio
        double _last_momentum_update
        object _momentum_threshold
        bint _adverse_momentum_detected
        object _last_price_for_momentum
        double _momentum_detection_window

        # ORDER BOOK INTELLIGENCE: Smart positioning
        object _order_book_levels
        object _support_resistance_levels
        object _bid_ask_spread_history
        object _order_book_imbalance
        double _last_orderbook_analysis
        object _optimal_bid_level
        object _optimal_ask_level
        object _book_depth_threshold
        object _spread_percentile_90
        object _spread_percentile_10

        # MARKET IMPACT MODELING: Footprint reduction
        object _our_market_share
        object _estimated_daily_volume
        object _our_daily_volume
        object _price_impact_model
        object _optimal_order_size
        object _max_single_order_impact
        object _volume_participation_rate
        double _last_impact_calculation
        object _historical_impacts
        object _size_impact_curve

    cdef object c_get_mid_price(self)
    cdef object c_create_timer(self, double timestamp, double time_delta_min, double time_delta_max)
    cdef object c_create_base_proposal(self)
    cdef tuple c_get_adjusted_available_balance(self, list orders)
    cdef c_apply_order_levels_modifiers(self, object proposal)
    cdef c_apply_price_band(self, object proposal)
    cdef c_apply_ping_pong(self, object proposal)
    cdef c_apply_order_price_modifiers(self, object proposal)
    cdef c_apply_order_size_modifiers(self, object proposal)
    cdef c_apply_inventory_skew(self, object proposal)
    cdef c_apply_budget_constraint(self, object proposal)
    cdef c_did_cancel_order(self, object order_cancelled_event)

    cdef c_filter_out_takers(self, object proposal)
    cdef c_apply_order_optimization(self, object proposal)
    cdef c_apply_add_transaction_costs(self, object proposal)
    cdef bint c_is_within_tolerance(self, list current_prices, list proposal_prices)
    cdef c_cancel_active_orders(self, object proposal)
    cdef c_cancel_orders_below_min_spread(self)
    cdef c_cancel_active_orders_on_max_age_limit(self)
    cdef bint c_to_create_orders(self, object proposal)
    cdef c_execute_orders_proposal(self, object proposal)
    cdef set_timers(self)
    cdef c_apply_moving_price_band(self, object proposal)
