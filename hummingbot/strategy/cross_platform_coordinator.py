#!/usr/bin/env python
"""
Cross-Platform Coordinator for MEXC-DEX Market Making

This module coordinates market making activities between MEXC exchange and Uniswap DEX
to minimize arbitrage risks and optimize order placement strategies.

Key Features:
- Real-time price deviation monitoring
- Arbitrage opportunity detection
- Dynamic spread adjustment
- Order coordination between platforms
- Risk management and emergency stops
"""

import asyncio
import logging
import time
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from hummingbot.core.utils.async_utils import safe_ensure_future
from hummingbot.logger import HummingbotLogger
from hummingbot.strategy.dex_asset_price_delegate import DexAssetPriceDelegate


class CrossPlatformCoordinator:
    """
    Coordinates market making activities between MEXC and DEX to minimize arbitrage risks
    """

    _logger: Optional[HummingbotLogger] = None

    def __init__(self,
                 mexc_market,
                 dex_price_delegate: DexAssetPriceDelegate,
                 max_price_deviation_pct: Decimal = Decimal("2.0"),
                 arbitrage_protection_spread: Decimal = Decimal("0.5"),
                 min_dex_liquidity_threshold: Decimal = Decimal("10000"),
                 low_liquidity_spread_multiplier: Decimal = Decimal("1.5"),
                 emergency_stop_deviation: Decimal = Decimal("5.0")):
        """
        Initialize cross-platform coordinator

        :param mexc_market: MEXC market connector
        :param dex_price_delegate: DEX price delegate for monitoring
        :param max_price_deviation_pct: Maximum allowed price deviation (%)
        :param arbitrage_protection_spread: Additional spread when arbitrage detected (%)
        :param min_dex_liquidity_threshold: Minimum DEX liquidity threshold (USD)
        :param low_liquidity_spread_multiplier: Spread multiplier for low liquidity
        :param emergency_stop_deviation: Emergency stop price deviation threshold (%)
        """
        self._mexc_market = mexc_market
        self._dex_price_delegate = dex_price_delegate
        self._max_price_deviation_pct = max_price_deviation_pct / 100  # Convert to decimal
        self._arbitrage_protection_spread = arbitrage_protection_spread / 100
        self._min_dex_liquidity_threshold = min_dex_liquidity_threshold
        self._low_liquidity_spread_multiplier = low_liquidity_spread_multiplier
        self._emergency_stop_deviation = emergency_stop_deviation / 100

        # State tracking
        self._last_mexc_price: Optional[Decimal] = None
        self._last_dex_price: Optional[Decimal] = None
        self._arbitrage_detected: bool = False
        self._low_liquidity_detected: bool = False
        self._emergency_stop_triggered: bool = False
        self._price_deviation_history: List[Tuple[float, Decimal]] = []

        # Monitoring task
        self._monitoring_task: Optional[asyncio.Task] = None
        self._monitoring_interval: float = 5.0  # Monitor every 5 seconds

        # Metrics
        self._arbitrage_opportunities_detected: int = 0
        self._emergency_stops_triggered: int = 0
        self._low_liquidity_events: int = 0

        self.start_monitoring()

    @classmethod
    def logger(cls) -> HummingbotLogger:
        if cls._logger is None:
            cls._logger = logging.getLogger(__name__)
        return cls._logger

    def start_monitoring(self):
        """Start the cross-platform monitoring task"""
        if self._monitoring_task is None or self._monitoring_task.done():
            self._monitoring_task = safe_ensure_future(self._monitoring_loop())
            self.logger().info("CROSS_PLATFORM_COORDINATOR: Started monitoring task (will wait for market readiness)")

    def stop_monitoring(self):
        """Stop the monitoring task"""
        if self._monitoring_task and not self._monitoring_task.done():
            self._monitoring_task.cancel()

    def stop(self):
        """Stop all coordinator activities"""
        self.stop_monitoring()

    async def _monitoring_loop(self):
        """Main monitoring loop for cross-platform coordination"""
        while True:
            try:
                await self._update_market_analysis()
                await asyncio.sleep(self._monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger().error(f"CROSS_PLATFORM_COORDINATOR: Error in monitoring loop: {str(e)}")
                await asyncio.sleep(self._monitoring_interval)

    async def _update_market_analysis(self):
        """Update market analysis and detect arbitrage opportunities"""
        try:
            # Get current prices
            mexc_price = await self._get_mexc_price()
            dex_price = self._dex_price_delegate.c_get_mid_price()

            if mexc_price is None or dex_price.is_nan():
                # Check if this is the first time we're waiting for market readiness
                if mexc_price is None and hasattr(self._mexc_market, 'ready') and not self._mexc_market.ready:
                    # Only log once every 30 seconds to avoid spam
                    current_time = time.time()
                    if not hasattr(self, '_last_readiness_log') or current_time - self._last_readiness_log > 30:
                        self.logger().info("CROSS_PLATFORM_COORDINATOR: Waiting for MEXC market to be ready...")
                        self._last_readiness_log = current_time
                return

            self._last_mexc_price = mexc_price
            self._last_dex_price = dex_price

            # Calculate price deviation
            price_deviation = abs(mexc_price - dex_price) / dex_price

            # Store price deviation history
            current_time = time.time()
            self._price_deviation_history.append((current_time, price_deviation))

            # Keep only last 100 data points
            if len(self._price_deviation_history) > 100:
                self._price_deviation_history.pop(0)

            # Check for arbitrage opportunities
            self._check_arbitrage_opportunity(mexc_price, dex_price, price_deviation)

            # Check DEX liquidity
            self._check_dex_liquidity()

            # Check for emergency stop conditions
            self._check_emergency_stop(price_deviation)

            # Log analysis periodically
            if int(current_time) % 60 == 0:  # Every minute
                self._log_market_analysis(mexc_price, dex_price, price_deviation)

        except Exception as e:
            self.logger().error(f"CROSS_PLATFORM_COORDINATOR: Error updating market analysis: {str(e)}")

    async def _get_mexc_price(self) -> Optional[Decimal]:
        """Get current MEXC price"""
        try:
            # Get the trading pair from the market
            trading_pairs = list(self._mexc_market.trading_pairs)
            if not trading_pairs:
                return None

            trading_pair = trading_pairs[0]

            # Check if the market is ready before trying to get price
            if not self._mexc_market.ready:
                # Market is not ready yet, return None silently
                return None

            # Check if order book exists before accessing it
            if not hasattr(self._mexc_market, '_order_book_tracker') or not self._mexc_market._order_book_tracker:
                return None

            # Check if the specific trading pair order book is available
            try:
                order_book = self._mexc_market.get_order_book(trading_pair)
                if not order_book:
                    return None
            except Exception:
                # Order book not ready yet, return None silently
                return None

            # Try different methods to get the price
            if hasattr(self._mexc_market, 'get_mid_price'):
                return self._mexc_market.get_mid_price(trading_pair)
            elif hasattr(self._mexc_market, 'get_price_by_type'):
                from hummingbot.core.data_type.common import PriceType
                return self._mexc_market.get_price_by_type(trading_pair, PriceType.MidPrice)
            else:
                # Fallback: calculate mid price from order book
                if order_book and order_book.bid_entries() and order_book.ask_entries():
                    best_bid = Decimal(str(list(order_book.bid_entries())[0].price))
                    best_ask = Decimal(str(list(order_book.ask_entries())[0].price))
                    return (best_bid + best_ask) / 2
            return None
        except Exception as e:
            # Only log errors if the market should be ready
            if hasattr(self._mexc_market, 'ready') and self._mexc_market.ready:
                self.logger().error(f"CROSS_PLATFORM_COORDINATOR: Error getting MEXC price: {str(e)}")
            return None

    def _check_arbitrage_opportunity(self, mexc_price: Decimal, dex_price: Decimal, deviation: Decimal):
        """Check for arbitrage opportunities and update state"""
        previous_arbitrage_state = self._arbitrage_detected

        if deviation > self._max_price_deviation_pct:
            if not self._arbitrage_detected:
                self._arbitrage_opportunities_detected += 1
                self.logger().warning(
                    f"CROSS_PLATFORM_COORDINATOR: Arbitrage opportunity detected! "
                    f"MEXC: ${mexc_price:.6f}, DEX: ${dex_price:.6f}, "
                    f"Deviation: {deviation:.2%}"
                )
            self._arbitrage_detected = True
        else:
            if self._arbitrage_detected:
                self.logger().info("CROSS_PLATFORM_COORDINATOR: Arbitrage opportunity resolved")
            self._arbitrage_detected = False

        # Log state change
        if previous_arbitrage_state != self._arbitrage_detected:
            self.logger().info(f"CROSS_PLATFORM_COORDINATOR: Arbitrage state changed to {self._arbitrage_detected}")

    def _check_dex_liquidity(self):
        """Check DEX liquidity levels"""
        previous_liquidity_state = self._low_liquidity_detected

        dex_liquidity = self._dex_price_delegate.liquidity_usd

        if dex_liquidity < self._min_dex_liquidity_threshold:
            if not self._low_liquidity_detected:
                self._low_liquidity_events += 1
                self.logger().warning(
                    f"CROSS_PLATFORM_COORDINATOR: Low DEX liquidity detected! "
                    f"Current: ${dex_liquidity:.2f}, Threshold: ${self._min_dex_liquidity_threshold:.2f}"
                )
            self._low_liquidity_detected = True
        else:
            if self._low_liquidity_detected:
                self.logger().info("CROSS_PLATFORM_COORDINATOR: DEX liquidity restored")
            self._low_liquidity_detected = False

        # Log state change
        if previous_liquidity_state != self._low_liquidity_detected:
            self.logger().info(f"CROSS_PLATFORM_COORDINATOR: Low liquidity state changed to {self._low_liquidity_detected}")

    def _check_emergency_stop(self, deviation: Decimal):
        """Check for emergency stop conditions"""
        if deviation > self._emergency_stop_deviation:
            if not self._emergency_stop_triggered:
                self._emergency_stops_triggered += 1
                self.logger().error(
                    f"CROSS_PLATFORM_COORDINATOR: EMERGENCY STOP TRIGGERED! "
                    f"Price deviation: {deviation:.2%} exceeds threshold: {self._emergency_stop_deviation:.2%}"
                )
            self._emergency_stop_triggered = True
        else:
            if self._emergency_stop_triggered:
                self.logger().info("CROSS_PLATFORM_COORDINATOR: Emergency stop condition resolved")
            self._emergency_stop_triggered = False

    def _log_market_analysis(self, mexc_price: Decimal, dex_price: Decimal, deviation: Decimal):
        """Log comprehensive market analysis"""
        dex_liquidity = self._dex_price_delegate.liquidity_usd
        dex_volume = self._dex_price_delegate.volume_24h_usd

        self.logger().info("=" * 60)
        self.logger().info("📊 CROSS-PLATFORM MARKET ANALYSIS")
        self.logger().info("=" * 60)
        self.logger().info(f"💱 MEXC Price: ${mexc_price:.6f}")
        self.logger().info(f"🔗 DEX Price: ${dex_price:.6f}")
        self.logger().info(f"📈 Price Deviation: {deviation:.2%}")
        self.logger().info(f"💧 DEX Liquidity: ${dex_liquidity:.2f}")
        self.logger().info(f"📊 DEX 24h Volume: ${dex_volume:.2f}")
        self.logger().info(f"⚠️  Arbitrage Detected: {self._arbitrage_detected}")
        self.logger().info(f"🔻 Low Liquidity: {self._low_liquidity_detected}")
        self.logger().info(f"🛑 Emergency Stop: {self._emergency_stop_triggered}")
        self.logger().info(f"🎯 Arbitrage Opportunities: {self._arbitrage_opportunities_detected}")
        self.logger().info(f"🚨 Emergency Stops: {self._emergency_stops_triggered}")
        self.logger().info("=" * 60)

    def get_spread_adjustment(self) -> Decimal:
        """Calculate spread adjustment based on current market conditions"""
        adjustment = Decimal("0")

        # Add arbitrage protection spread
        if self._arbitrage_detected:
            adjustment += self._arbitrage_protection_spread

        # Add low liquidity spread multiplier
        if self._low_liquidity_detected:
            adjustment *= self._low_liquidity_spread_multiplier

        return adjustment

    def should_place_orders(self) -> bool:
        """Determine if orders should be placed based on current conditions"""
        if self._emergency_stop_triggered:
            return False

        # Could add more sophisticated logic here
        return True

    def get_market_conditions(self) -> Dict:
        """Get current market conditions summary"""
        return {
            'mexc_price': self._last_mexc_price,
            'dex_price': self._last_dex_price,
            'price_deviation': abs(self._last_mexc_price - self._last_dex_price) / self._last_dex_price if self._last_mexc_price and self._last_dex_price else None,
            'arbitrage_detected': self._arbitrage_detected,
            'low_liquidity_detected': self._low_liquidity_detected,
            'emergency_stop_triggered': self._emergency_stop_triggered,
            'dex_liquidity_usd': self._dex_price_delegate.liquidity_usd,
            'dex_volume_24h_usd': self._dex_price_delegate.volume_24h_usd,
            'spread_adjustment': self.get_spread_adjustment(),
            'should_place_orders': self.should_place_orders()
        }

    @property
    def arbitrage_detected(self) -> bool:
        """Check if arbitrage opportunity is currently detected"""
        return self._arbitrage_detected

    @property
    def emergency_stop_triggered(self) -> bool:
        """Check if emergency stop is triggered"""
        return self._emergency_stop_triggered

    @property
    def low_liquidity_detected(self) -> bool:
        """Check if low liquidity is detected"""
        return self._low_liquidity_detected
