from decimal import Decimal
from typing import Optional

from hummingbot.client.config.config_validators import (
    validate_bool,
    validate_connector,
    validate_decimal,
    validate_int,
    validate_market_trading_pair,
)
from hummingbot.client.config.config_var import ConfigVar
from hummingbot.client.settings import AllConnectorSettings


def maker_trading_pair_prompt():
    exchange = volume_generation_config_map.get("exchange").value
    example = AllConnectorSettings.get_example_pairs().get(exchange)
    return "Enter the token trading pair you would like to trade on %s%s >>> " \
           % (exchange, f" (e.g. {example})" if example else "")


def validate_exchange_trading_pair(value: str) -> Optional[str]:
    exchange = volume_generation_config_map.get("exchange").value
    return validate_market_trading_pair(exchange, value)


def order_amount_prompt() -> str:
    trading_pair = volume_generation_config_map["market"].value
    base_asset, quote_asset = trading_pair.split("-")
    return f"What is the amount of {base_asset} per order? >>> "


volume_generation_config_map = {
    "strategy": ConfigVar(
        key="strategy",
        prompt="",
        default="volume_generation"),
    "exchange": ConfigVar(
        key="exchange",
        prompt="Enter your maker spot connector >>> ",
        validator=validate_connector,
        on_validated=lambda value: required_exchanges.append(value),
        prompt_on_new=True),
    "market": ConfigVar(
        key="market",
        prompt=maker_trading_pair_prompt,
        validator=validate_exchange_trading_pair,
        prompt_on_new=True),
    "daily_volume_target_usd": ConfigVar(
        key="daily_volume_target_usd",
        prompt="Enter your daily volume target in USD (e.g., 50000 for $50K) >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("100"), inclusive=False),
        default=Decimal("50000")),
    "max_daily_loss_usd": ConfigVar(
        key="max_daily_loss_usd",
        prompt="Enter maximum daily loss in USD (e.g., 50 for $50) >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("1"), inclusive=False),
        default=Decimal("50")),
    "max_single_order_loss_usd": ConfigVar(
        key="max_single_order_loss_usd",
        prompt="Enter maximum loss per single order in USD (e.g., 2 for $2) >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("0.1"), inclusive=False),
        default=Decimal("2")),
    "min_order_size_usd": ConfigVar(
        key="min_order_size_usd",
        prompt="Enter minimum order size in USD >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("1"), inclusive=False),
        default=Decimal("10")),
    "max_order_size_usd": ConfigVar(
        key="max_order_size_usd",
        prompt="Enter maximum order size in USD >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("10"), inclusive=False),
        default=Decimal("500")),
    "min_spread_pct": ConfigVar(
        key="min_spread_pct",
        prompt="Enter minimum spread percentage (e.g., 0.1 for 0.1%) >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("0.01"), max_value=Decimal("5"), inclusive=True),
        default=Decimal("0.1")),
    "max_spread_pct": ConfigVar(
        key="max_spread_pct",
        prompt="Enter maximum spread percentage (e.g., 0.5 for 0.5%) >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("0.1"), max_value=Decimal("10"), inclusive=True),
        default=Decimal("0.5")),
    "order_frequency_seconds": ConfigVar(
        key="order_frequency_seconds",
        prompt="How often to create volume orders (in seconds) >>> ",
        type_str="int",
        validator=lambda v: validate_int(v, min_value=10, inclusive=True),
        default=30),
    "enable_market_stress_protection": ConfigVar(
        key="enable_market_stress_protection",
        prompt="Enable market stress protection? (Yes/No) >>> ",
        type_str="bool",
        default=True,
        validator=validate_bool),
    "market_stress_threshold_pct": ConfigVar(
        key="market_stress_threshold_pct",
        prompt="Market stress threshold percentage (e.g., 2 for 2%) >>> ",
        type_str="decimal",
        validator=lambda v: validate_decimal(v, min_value=Decimal("0.5"), max_value=Decimal("10"), inclusive=True),
        default=Decimal("2"),
        required_if=lambda: volume_generation_config_map.get("enable_market_stress_protection").value),
    "enable_emergency_stop": ConfigVar(
        key="enable_emergency_stop",
        prompt="Enable emergency stop when limits are reached? (Yes/No) >>> ",
        type_str="bool",
        default=True,
        validator=validate_bool),
    "order_refresh_time": ConfigVar(
        key="order_refresh_time",
        prompt="How often to refresh orders (in seconds) >>> ",
        type_str="float",
        validator=lambda v: validate_decimal(v, min_value=Decimal("5"), inclusive=True),
        default=60.0),
    "enable_volume_distribution": ConfigVar(
        key="enable_volume_distribution",
        prompt="Distribute volume evenly throughout the day? (Yes/No) >>> ",
        type_str="bool",
        default=True,
        validator=validate_bool),
    "execution_method": ConfigVar(
        key="execution_method",
        prompt="Volume execution method (self_execution/market_orders/aggressive_limit/traditional_limit) >>> ",
        type_str="str",
        default="self_execution",
        validator=lambda v: None if v in ["self_execution", "market_orders", "aggressive_limit", "traditional_limit"] else "Invalid execution method"),
    "logging_level": ConfigVar(
        key="logging_level",
        prompt="Logging level (INFO/WARNING/ERROR) >>> ",
        type_str="str",
        default="INFO",
        validator=lambda v: None if v.upper() in ["INFO", "WARNING", "ERROR"] else "Invalid log level"),
}

required_exchanges = []
