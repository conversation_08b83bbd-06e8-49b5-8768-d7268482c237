from decimal import Decimal
from typing import List

from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.volume_generation import VolumeGenerationStrategy
from hummingbot.strategy.volume_generation.volume_generation_config_map import volume_generation_config_map as c_map


def start(self):
    try:
        exchange = c_map.get("exchange").value.lower()
        raw_market_symbol = c_map.get("market").value
        daily_volume_target_usd = c_map.get("daily_volume_target_usd").value
        max_daily_loss_usd = c_map.get("max_daily_loss_usd").value
        max_single_order_loss_usd = c_map.get("max_single_order_loss_usd").value
        min_order_size_usd = c_map.get("min_order_size_usd").value
        max_order_size_usd = c_map.get("max_order_size_usd").value
        min_spread_pct = c_map.get("min_spread_pct").value / Decimal("100")
        max_spread_pct = c_map.get("max_spread_pct").value / Decimal("100")
        order_frequency_seconds = c_map.get("order_frequency_seconds").value
        enable_market_stress_protection = c_map.get("enable_market_stress_protection").value
        market_stress_threshold_pct = c_map.get("market_stress_threshold_pct").value / Decimal("100")
        enable_emergency_stop = c_map.get("enable_emergency_stop").value
        order_refresh_time = c_map.get("order_refresh_time").value
        enable_volume_distribution = c_map.get("enable_volume_distribution").value
        execution_method = c_map.get("execution_method").value
        logging_level = c_map.get("logging_level").value

        try:
            trading_pair: str = raw_market_symbol
            assets: List[str] = [trading_pair.split("-")[0], trading_pair.split("-")[1]]
        except ValueError as e:
            self.notify(f"Invalid trading pair {raw_market_symbol}: {e}")
            return

        market_names: List[str] = [exchange]
        self._initialize_markets(market_names)
        exchange_trading_pair = raw_market_symbol
        market = self.markets[exchange]
        market_trading_pair_tuple = MarketTradingPairTuple(market, exchange_trading_pair, *assets)

        strategy_logging_options = VolumeGenerationStrategy.OPTION_LOG_ALL
        self.strategy = VolumeGenerationStrategy()
        self.strategy.init_params(
            market_info=market_trading_pair_tuple,
            daily_volume_target_usd=daily_volume_target_usd,
            max_daily_loss_usd=max_daily_loss_usd,
            max_single_order_loss_usd=max_single_order_loss_usd,
            min_order_size_usd=min_order_size_usd,
            max_order_size_usd=max_order_size_usd,
            min_spread_pct=min_spread_pct,
            max_spread_pct=max_spread_pct,
            order_frequency_seconds=order_frequency_seconds,
            enable_market_stress_protection=enable_market_stress_protection,
            market_stress_threshold_pct=market_stress_threshold_pct,
            enable_emergency_stop=enable_emergency_stop,
            order_refresh_time=order_refresh_time,
            enable_volume_distribution=enable_volume_distribution,
            execution_method=execution_method,
            logging_options=strategy_logging_options,
            logging_level=logging_level,
        )
    except Exception as e:
        self.notify(f"Error initializing volume generation strategy: {e}")
        self.logger().error("Unknown error during initialization.", exc_info=True)
