import asyncio
import logging
import random
import time
from collections import deque
from decimal import Decimal
from typing import Dict, List, Set

from hummingbot.core.data_type.common import OrderType
from hummingbot.core.data_type.limit_order import LimitOrder
from hummingbot.core.event.events import (
    BuyOrderCompletedEvent,
    OrderCancelledEvent,
    OrderFilledEvent,
    SellOrderCompletedEvent,
)
from hummingbot.core.utils.async_utils import safe_ensure_future
from hummingbot.logger import HummingbotLogger
from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.strategy_base import StrategyBase

s_decimal_zero = Decimal(0)
s_decimal_one = Decimal(1)
s_logger = None


class VolumeGenerationStrategy(StrategyBase):
    """
    Volume Generation Strategy for Hummingbot

    Creates trading volume through controlled buy/sell pairs while minimizing losses
    and protecting against market manipulation.
    """

    OPTION_LOG_CREATE_ORDER = 1 << 3
    OPTION_LOG_MAKER_ORDER_FILLED = 1 << 4
    OPTION_LOG_STATUS_REPORT = 1 << 5
    OPTION_LOG_ALL = 0x7fffffffffffffff

    @classmethod
    def logger(cls) -> HummingbotLogger:
        global s_logger
        if s_logger is None:
            s_logger = logging.getLogger(__name__)
        return s_logger

    def __init__(self):
        super().__init__()

        # Strategy parameters (will be set in init_params)
        self._market_info = None
        self._daily_volume_target_usd = Decimal("50000")
        self._max_daily_loss_usd = Decimal("10")
        self._max_single_order_loss_usd = Decimal("0.1")
        self._min_order_size_usd = Decimal("10")
        self._max_order_size_usd = Decimal("500")
        self._min_spread_pct = Decimal("0.001")  # 0.1%
        self._max_spread_pct = Decimal("0.005")  # 0.5%
        self._order_frequency_seconds = 60
        self._enable_market_stress_protection = True
        self._market_stress_threshold_pct = Decimal("0.02")  # 2%
        self._enable_emergency_stop = True
        self._order_refresh_time = 60.0
        self._enable_volume_distribution = True
        self._logging_options = self.OPTION_LOG_ALL
        self._logging_level = "INFO"

        # Tracking variables
        self._current_daily_volume_usd = Decimal("0")
        self._current_daily_loss_usd = Decimal("0")
        self._current_hourly_loss_usd = Decimal("0")
        self._last_daily_reset = time.time()
        self._last_hourly_reset = time.time()
        self._last_order_creation = 0.0

        # Order management
        self._active_volume_orders: Set[str] = set()
        self._order_pairs: Dict[str, Dict] = {}  # Track buy/sell pairs
        self._recent_executions = deque(maxlen=100)

        # Market monitoring
        self._price_history = deque(maxlen=60)  # Last 60 price points
        self._last_price = Decimal("0")
        self._market_stress_detected = False
        self._emergency_stop_triggered = False

        # Performance tracking
        self._total_orders_created = 0
        self._total_orders_filled = 0
        self._total_volume_generated_usd = Decimal("0")
        self._total_losses_usd = Decimal("0")
        self._total_gains_usd = Decimal("0")

    def init_params(self,
                    market_info: MarketTradingPairTuple,
                    daily_volume_target_usd: Decimal = Decimal("50000"),
                    max_daily_loss_usd: Decimal = Decimal("50"),
                    max_single_order_loss_usd: Decimal = Decimal("2"),
                    min_order_size_usd: Decimal = Decimal("10"),
                    max_order_size_usd: Decimal = Decimal("500"),
                    min_spread_pct: Decimal = Decimal("0.001"),
                    max_spread_pct: Decimal = Decimal("0.005"),
                    order_frequency_seconds: int = 30,
                    enable_market_stress_protection: bool = True,
                    market_stress_threshold_pct: Decimal = Decimal("0.02"),
                    enable_emergency_stop: bool = True,
                    order_refresh_time: float = 60.0,
                    enable_volume_distribution: bool = True,
                    execution_method: str = "self_execution",
                    logging_options: int = OPTION_LOG_ALL,
                    logging_level: str = "INFO"):

        self._market_info = market_info
        self._daily_volume_target_usd = daily_volume_target_usd
        self._max_daily_loss_usd = max_daily_loss_usd
        self._max_single_order_loss_usd = max_single_order_loss_usd
        self._min_order_size_usd = min_order_size_usd
        self._max_order_size_usd = max_order_size_usd
        self._min_spread_pct = min_spread_pct
        self._max_spread_pct = max_spread_pct
        self._order_frequency_seconds = order_frequency_seconds
        self._enable_market_stress_protection = enable_market_stress_protection
        self._market_stress_threshold_pct = market_stress_threshold_pct
        self._enable_emergency_stop = enable_emergency_stop
        self._order_refresh_time = order_refresh_time
        self._enable_volume_distribution = enable_volume_distribution
        self._execution_method = execution_method
        self._logging_options = logging_options
        self._logging_level = logging_level.upper()

        self.c_add_markets([market_info.market])

    @property
    def market_info(self) -> MarketTradingPairTuple:
        return self._market_info

    @property
    def active_orders(self) -> List[LimitOrder]:
        return [order for order in self.market_info.market.limit_orders if order.client_order_id in self._active_volume_orders]

    def tick(self, timestamp: float):
        """
        Main strategy tick - called every second
        """
        if not self.all_markets_ready():
            return

        # Reset daily/hourly counters if needed
        self._reset_counters_if_needed(timestamp)

        # Update market monitoring
        self._update_market_monitoring(timestamp)

        # Check if we should create new volume orders
        if self._should_create_volume_orders(timestamp):
            safe_ensure_future(self._create_volume_orders())

        # Cancel old orders if needed
        self._cancel_old_orders(timestamp)

    def _reset_counters_if_needed(self, timestamp: float):
        """Reset daily and hourly counters"""
        # Reset daily counters (24 hours)
        if timestamp - self._last_daily_reset > 86400:
            self._current_daily_volume_usd = Decimal("0")
            self._current_daily_loss_usd = Decimal("0")
            self._last_daily_reset = timestamp
            self.logger().info("Daily counters reset")

        # Reset hourly counters (1 hour)
        if timestamp - self._last_hourly_reset > 3600:
            self._current_hourly_loss_usd = Decimal("0")
            self._last_hourly_reset = timestamp

    def _update_market_monitoring(self, timestamp: float):
        """Update market stress monitoring"""
        current_price = self.market_info.get_mid_price()
        if current_price and current_price > 0:
            self._price_history.append((timestamp, current_price))
            self._last_price = current_price

            # Check for market stress
            if self._enable_market_stress_protection:
                self._check_market_stress()

    def _check_market_stress(self):
        """Check if market is under stress (high volatility)"""
        if len(self._price_history) < 10:
            return

        # Calculate price volatility over last 10 data points
        recent_prices = [price for _, price in list(self._price_history)[-10:]]
        if len(recent_prices) < 2:
            return

        price_changes = []
        for i in range(1, len(recent_prices)):
            change = abs(recent_prices[i] - recent_prices[i - 1]) / recent_prices[i - 1]
            price_changes.append(change)

        avg_volatility = sum(price_changes) / len(price_changes)

        if avg_volatility > self._market_stress_threshold_pct:
            if not self._market_stress_detected:
                self.logger().warning(f"Market stress detected! Volatility: {avg_volatility:.4f}")
                self._market_stress_detected = True
        else:
            if self._market_stress_detected:
                self.logger().info("Market stress cleared")
                self._market_stress_detected = False

    def _should_create_volume_orders(self, timestamp: float) -> bool:
        """Determine if we should create new volume orders"""
        # Check if emergency stop is triggered
        if self._emergency_stop_triggered:
            return False

        # Check frequency limit
        if timestamp - self._last_order_creation < self._order_frequency_seconds:
            return False

        # Check daily volume target
        if self._current_daily_volume_usd >= self._daily_volume_target_usd:
            return False

        # Check daily loss limit
        if self._current_daily_loss_usd >= self._max_daily_loss_usd:
            if self._enable_emergency_stop:
                self._trigger_emergency_stop("Daily loss limit reached")
            return False

        # Check market stress
        if self._market_stress_detected and self._enable_market_stress_protection:
            return False

        # Check if we have too many active orders
        if len(self._active_volume_orders) >= 10:  # Max 10 active volume orders
            return False

        return True

    async def _create_volume_orders(self):
        """Create volume through guaranteed execution methods"""
        try:
            current_price = self.market_info.get_mid_price()
            if not current_price or current_price <= 0:
                return

            # Generate order parameters
            order_size_usd = self._generate_order_size()

            # Choose execution method based on configuration
            execution_method = getattr(self, '_execution_method', 'self_execution')

            if execution_method == 'self_execution':
                await self._create_self_executing_orders(current_price, order_size_usd)
            elif execution_method == 'market_orders':
                await self._create_market_order_volume(current_price, order_size_usd)
            elif execution_method == 'aggressive_limit':
                await self._create_aggressive_limit_orders(current_price, order_size_usd)
            else:
                # Fallback to original method
                await self._create_traditional_limit_orders(current_price, order_size_usd)

        except Exception as e:
            self.logger().error(f"Error creating volume orders: {e}")

    async def _create_self_executing_orders(self, current_price: Decimal, order_size_usd: Decimal):
        """
        Method 1: Self-Execution Strategy
        Creates orders that we immediately execute ourselves
        """
        try:
            spread_pct = self._generate_spread()
            pair_id = f"vol_self_{int(time.time() * 1000)}"

            # Step 1: Place a buy order slightly below market
            buy_price = current_price * (s_decimal_one - spread_pct)
            buy_amount = order_size_usd / buy_price

            buy_order_id = self.market_info.market.buy(
                trading_pair=self.market_info.trading_pair,
                amount=buy_amount,
                order_type=OrderType.LIMIT,
                price=buy_price
            )

            # Step 2: Wait a few seconds for order to be placed
            await asyncio.sleep(2)

            # Step 3: Execute the buy order with a market sell
            # This creates guaranteed volume
            sell_order_id = self.market_info.market.sell(
                trading_pair=self.market_info.trading_pair,
                amount=buy_amount,
                order_type=OrderType.MARKET
            )

            # Track the orders
            self._active_volume_orders.add(buy_order_id)
            self._active_volume_orders.add(sell_order_id)

            # Store order pair information
            self._order_pairs[pair_id] = {
                'buy_order_id': buy_order_id,
                'sell_order_id': sell_order_id,
                'buy_price': buy_price,
                'sell_price': current_price,  # Market price for sell
                'buy_amount': buy_amount,
                'sell_amount': buy_amount,
                'order_size_usd': order_size_usd,
                'spread_pct': spread_pct,
                'created_timestamp': time.time(),
                'expected_loss': order_size_usd * spread_pct,  # Loss from spread
                'execution_method': 'self_execution'
            }

            self._total_orders_created += 2
            self._last_order_creation = time.time()

            self.logger().info(f"Created self-executing volume pair {pair_id}: "
                               f"Buy ${order_size_usd:.2f} @ {buy_price:.6f}, "
                               f"Market Sell @ ~{current_price:.6f}, "
                               f"Expected Loss: ${order_size_usd * spread_pct:.2f}")

        except Exception as e:
            self.logger().error(f"Error in self-executing orders: {e}")

    async def _create_market_order_volume(self, current_price: Decimal, order_size_usd: Decimal):
        """
        Method 2: Market Order Volume Generation
        Creates immediate volume through market orders
        """
        try:
            pair_id = f"vol_market_{int(time.time() * 1000)}"

            # Calculate amounts for buy and sell
            buy_amount = (order_size_usd / 2) / current_price
            sell_amount = (order_size_usd / 2) / current_price

            # Create market buy order
            buy_order_id = self.market_info.market.buy(
                trading_pair=self.market_info.trading_pair,
                amount=buy_amount,
                order_type=OrderType.MARKET
            )

            # Wait a moment
            await asyncio.sleep(1)

            # Create market sell order
            sell_order_id = self.market_info.market.sell(
                trading_pair=self.market_info.trading_pair,
                amount=sell_amount,
                order_type=OrderType.MARKET
            )

            # Track the orders
            self._active_volume_orders.add(buy_order_id)
            self._active_volume_orders.add(sell_order_id)

            # Store order information
            self._order_pairs[pair_id] = {
                'buy_order_id': buy_order_id,
                'sell_order_id': sell_order_id,
                'buy_price': current_price,  # Approximate
                'sell_price': current_price,  # Approximate
                'buy_amount': buy_amount,
                'sell_amount': sell_amount,
                'order_size_usd': order_size_usd,
                'spread_pct': Decimal('0'),  # No spread for market orders
                'created_timestamp': time.time(),
                'expected_loss': order_size_usd * Decimal('0.001'),  # Trading fees only
                'execution_method': 'market_orders'
            }

            self._total_orders_created += 2
            self._last_order_creation = time.time()

            self.logger().info(f"Created market order volume {pair_id}: "
                               f"Market Buy ${order_size_usd / 2:.2f}, "
                               f"Market Sell ${order_size_usd / 2:.2f}")

        except Exception as e:
            self.logger().error(f"Error in market order volume: {e}")

    async def _create_aggressive_limit_orders(self, current_price: Decimal, order_size_usd: Decimal):
        """
        Method 3: Aggressive Limit Orders
        Places limit orders that are likely to execute immediately
        """
        try:
            order_book = self.market_info.market.get_order_book(self.market_info.trading_pair)
            if not order_book:
                return

            spread_pct = self._generate_spread()
            pair_id = f"vol_aggressive_{int(time.time() * 1000)}"

            # Get best bid and ask
            best_bid = order_book.get_price(False)  # Best bid
            best_ask = order_book.get_price(True)   # Best ask

            if not best_bid or not best_ask:
                return

            # Place aggressive orders that will likely execute
            # Buy at or above best ask (will execute against sells)
            aggressive_buy_price = best_ask * (s_decimal_one + spread_pct)
            buy_amount = order_size_usd / aggressive_buy_price

            # Sell at or below best bid (will execute against buys)
            aggressive_sell_price = best_bid * (s_decimal_one - spread_pct)
            sell_amount = order_size_usd / aggressive_sell_price

            # Create aggressive buy order
            buy_order_id = self.market_info.market.buy(
                trading_pair=self.market_info.trading_pair,
                amount=buy_amount,
                order_type=OrderType.LIMIT,
                price=aggressive_buy_price
            )

            # Wait a moment
            await asyncio.sleep(1)

            # Create aggressive sell order
            sell_order_id = self.market_info.market.sell(
                trading_pair=self.market_info.trading_pair,
                amount=sell_amount,
                order_type=OrderType.LIMIT,
                price=aggressive_sell_price
            )

            # Track the orders
            self._active_volume_orders.add(buy_order_id)
            self._active_volume_orders.add(sell_order_id)

            # Store order information
            self._order_pairs[pair_id] = {
                'buy_order_id': buy_order_id,
                'sell_order_id': sell_order_id,
                'buy_price': aggressive_buy_price,
                'sell_price': aggressive_sell_price,
                'buy_amount': buy_amount,
                'sell_amount': sell_amount,
                'order_size_usd': order_size_usd,
                'spread_pct': spread_pct,
                'created_timestamp': time.time(),
                'expected_loss': order_size_usd * spread_pct * 2,
                'execution_method': 'aggressive_limit'
            }

            self._total_orders_created += 2
            self._last_order_creation = time.time()

            self.logger().info(f"Created aggressive limit orders {pair_id}: "
                               f"Aggressive Buy @ {aggressive_buy_price:.6f}, "
                               f"Aggressive Sell @ {aggressive_sell_price:.6f}")

        except Exception as e:
            self.logger().error(f"Error in aggressive limit orders: {e}")

    async def _create_traditional_limit_orders(self, current_price: Decimal, order_size_usd: Decimal):
        """
        Method 4: Traditional Limit Orders (Original Method)
        Places orders and hopes they get executed by other traders
        """
        spread_pct = self._generate_spread()

        # Calculate order prices
        buy_price = current_price * (s_decimal_one - spread_pct)
        sell_price = current_price * (s_decimal_one + spread_pct)

        # Calculate order amounts
        buy_amount = order_size_usd / buy_price
        sell_amount = order_size_usd / sell_price

        # Create unique pair ID
        pair_id = f"vol_traditional_{int(time.time() * 1000)}"

        # Create buy order
        buy_order_id = self.market_info.market.buy(
            trading_pair=self.market_info.trading_pair,
            amount=buy_amount,
            order_type=OrderType.LIMIT,
            price=buy_price
        )

        # Create sell order
        sell_order_id = self.market_info.market.sell(
            trading_pair=self.market_info.trading_pair,
            amount=sell_amount,
            order_type=OrderType.LIMIT,
            price=sell_price
        )

        # Track the orders
        self._active_volume_orders.add(buy_order_id)
        self._active_volume_orders.add(sell_order_id)

        # Store order pair information
        self._order_pairs[pair_id] = {
            'buy_order_id': buy_order_id,
            'sell_order_id': sell_order_id,
            'buy_price': buy_price,
            'sell_price': sell_price,
            'buy_amount': buy_amount,
            'sell_amount': sell_amount,
            'order_size_usd': order_size_usd,
            'spread_pct': spread_pct,
            'created_timestamp': time.time(),
            'expected_loss': order_size_usd * spread_pct * 2,
            'execution_method': 'traditional_limit'
        }

        self._total_orders_created += 2
        self._last_order_creation = time.time()

        self.logger().info(f"Created traditional limit pair {pair_id}: "
                           f"Buy ${order_size_usd:.2f} @ {buy_price:.6f}, "
                           f"Sell ${order_size_usd:.2f} @ {sell_price:.6f}, "
                           f"Spread: {spread_pct:.4f}%")

    def _generate_order_size(self) -> Decimal:
        """Generate random order size within limits"""
        min_size = float(self._min_order_size_usd)
        max_size = float(self._max_order_size_usd)

        # Use weighted random to favor smaller orders
        weights = [0.4, 0.3, 0.2, 0.1]  # Favor smaller orders
        size_ranges = [
            (min_size, min_size * 2),
            (min_size * 2, min_size * 4),
            (min_size * 4, max_size * 0.7),
            (max_size * 0.7, max_size)
        ]

        selected_range = random.choices(size_ranges, weights=weights)[0]
        size = random.uniform(selected_range[0], selected_range[1])

        return Decimal(str(round(size, 2)))

    def _generate_spread(self) -> Decimal:
        """Generate random spread within limits"""
        min_spread = float(self._min_spread_pct)
        max_spread = float(self._max_spread_pct)

        # Use normal distribution centered around min_spread
        spread = random.normalvariate(min_spread * 1.5, min_spread * 0.5)
        spread = max(min_spread, min(max_spread, spread))

        return Decimal(str(round(spread, 6)))

    def _cancel_old_orders(self, timestamp: float):
        """Cancel orders that are too old"""
        orders_to_cancel = []

        for pair_id, pair_info in self._order_pairs.items():
            if timestamp - pair_info['created_timestamp'] > self._order_refresh_time:
                orders_to_cancel.append(pair_id)

        for pair_id in orders_to_cancel:
            self._cancel_order_pair(pair_id)

    def _cancel_order_pair(self, pair_id: str):
        """Cancel a specific order pair"""
        if pair_id not in self._order_pairs:
            return

        pair_info = self._order_pairs[pair_id]

        # Cancel buy order
        if pair_info['buy_order_id'] in self._active_volume_orders:
            self.market_info.market.cancel(self.market_info.trading_pair, pair_info['buy_order_id'])

        # Cancel sell order
        if pair_info['sell_order_id'] in self._active_volume_orders:
            self.market_info.market.cancel(self.market_info.trading_pair, pair_info['sell_order_id'])

        # Remove from tracking
        self._active_volume_orders.discard(pair_info['buy_order_id'])
        self._active_volume_orders.discard(pair_info['sell_order_id'])
        del self._order_pairs[pair_id]

        self.logger().info(f"Cancelled order pair {pair_id}")

    def _trigger_emergency_stop(self, reason: str):
        """Trigger emergency stop"""
        if self._emergency_stop_triggered:
            return

        self._emergency_stop_triggered = True
        self.logger().error(f"EMERGENCY STOP TRIGGERED: {reason}")

        # Cancel all active volume orders
        for order_id in list(self._active_volume_orders):
            self.market_info.market.cancel(self.market_info.trading_pair, order_id)

        self._active_volume_orders.clear()
        self._order_pairs.clear()

    # Event handlers
    def did_fill_order(self, event: OrderFilledEvent):
        """Handle order fill events"""
        if event.order_id not in self._active_volume_orders:
            return

        # Find the order pair
        pair_id = None
        for pid, pair_info in self._order_pairs.items():
            if event.order_id in [pair_info['buy_order_id'], pair_info['sell_order_id']]:
                pair_id = pid
                break

        if not pair_id:
            return

        # Update volume tracking
        fill_value_usd = event.amount * event.price
        self._current_daily_volume_usd += fill_value_usd
        self._total_volume_generated_usd += fill_value_usd
        self._total_orders_filled += 1

        # Track execution
        self._recent_executions.append({
            'timestamp': time.time(),
            'order_id': event.order_id,
            'pair_id': pair_id,
            'side': event.trade_type.name,
            'amount': event.amount,
            'price': event.price,
            'value_usd': fill_value_usd
        })

        self.logger().info(f"Volume order filled: {event.trade_type.name} "
                           f"{event.amount:.6f} @ {event.price:.6f} "
                           f"(${fill_value_usd:.2f})")

    def did_complete_buy_order(self, event: BuyOrderCompletedEvent):
        """Handle buy order completion"""
        if event.order_id in self._active_volume_orders:
            self._active_volume_orders.discard(event.order_id)

    def did_complete_sell_order(self, event: SellOrderCompletedEvent):
        """Handle sell order completion"""
        if event.order_id in self._active_volume_orders:
            self._active_volume_orders.discard(event.order_id)

    def did_cancel_order(self, event: OrderCancelledEvent):
        """Handle order cancellation"""
        if event.order_id in self._active_volume_orders:
            self._active_volume_orders.discard(event.order_id)

    def format_status(self) -> str:
        """Format strategy status for display"""
        if not self.all_markets_ready():
            return "Markets not ready"

        current_price = self.market_info.get_mid_price()

        lines = []
        lines.append("Volume Generation Strategy Status")
        lines.append(f"Market: {self.market_info.trading_pair}")
        lines.append(f"Current Price: {current_price:.6f}" if current_price else "Current Price: N/A")
        lines.append("")

        # Daily progress
        daily_progress = (self._current_daily_volume_usd / self._daily_volume_target_usd) * 100
        lines.append(f"Daily Volume: ${self._current_daily_volume_usd:.2f} / ${self._daily_volume_target_usd:.2f} ({daily_progress:.1f}%)")
        lines.append(f"Daily Loss: ${self._current_daily_loss_usd:.2f} / ${self._max_daily_loss_usd:.2f}")
        lines.append("")

        # Active orders
        lines.append(f"Active Volume Orders: {len(self._active_volume_orders)}")
        lines.append(f"Active Order Pairs: {len(self._order_pairs)}")
        lines.append("")

        # Performance
        lines.append(f"Total Orders Created: {self._total_orders_created}")
        lines.append(f"Total Orders Filled: {self._total_orders_filled}")
        lines.append(f"Total Volume Generated: ${self._total_volume_generated_usd:.2f}")
        lines.append("")

        # Status indicators
        if self._emergency_stop_triggered:
            lines.append("⚠️  EMERGENCY STOP ACTIVE")
        elif self._market_stress_detected:
            lines.append("⚠️  Market stress detected - paused")
        elif daily_progress >= 100:
            lines.append("✅ Daily target reached")
        else:
            lines.append("🔄 Generating volume...")

        return "\n".join(lines)
