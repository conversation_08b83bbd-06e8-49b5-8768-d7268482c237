[Unit]
Description=Hummingbot Trading Bot Service
Documentation=https://docs.hummingbot.org/
After=network-online.target
Wants=network-online.target
StartLimitIntervalSec=0

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/hummingbot
ExecStart=/root/hummingbot/hummingbot-service.sh
ExecStop=/root/hummingbot/graceful-stop.sh
ExecStopPost=/bin/bash -c 'rm -f /var/run/hummingbot.pid'

# Restart configuration
Restart=on-failure
RestartSec=30
StartLimitBurst=3

# Environment
Environment=PYTHONUNBUFFERED=1

# Logging
StandardOutput=append:/var/log/hummingbot/hummingbot-service.log
StandardError=append:/var/log/hummingbot/hummingbot-error.log

# Graceful shutdown timeout
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
