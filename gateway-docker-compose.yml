version: '3.8'

services:
  gateway:
    image: hummingbot/gateway:latest
    ports:
      - "15888:15888"
    volumes:
      - ~/gateway_certs:/home/<USER>/certs
    environment:
      - GATEWAY_PASSPHRASE=your_secure_passphrase_here
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "https://localhost:15888/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - hummingbot-network

networks:
  hummingbot-network:
    driver: bridge
