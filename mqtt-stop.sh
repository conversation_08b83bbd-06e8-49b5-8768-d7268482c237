#!/bin/bash

LOG_FILE="/var/log/hummingbot/hummingbot-service.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - MQTT-STOP: $1" | tee -a "$LOG_FILE"
}

# Get the correct instance ID and namespace from config
INSTANCE_ID=$(grep "instance_id:" /root/hummingbot/conf/conf_client.yml | awk '{print $2}' | tr -d '"')
NAMESPACE=$(grep -A 10 "mqtt_bridge:" /root/hummingbot/conf/conf_client.yml | grep "mqtt_namespace:" | awk '{print $2}' | tr -d '"')

log_message "Using instance_id: $INSTANCE_ID"
log_message "Using namespace: $NAMESPACE"

# Send stop command with EXACT same format as manual command
TOPIC="$NAMESPACE/$INSTANCE_ID/stop"

log_message "Sending MQTT stop command to topic: $TOPIC"

# Use the exact same command that works manually
mosquitto_pub -h localhost -t "$TOPIC" -m '{"header": {"reply_to": "test/reply"}, "data": {"skip_order_cancellation": false, "async_backend": true}}'

log_message "MQTT stop command sent successfully"
