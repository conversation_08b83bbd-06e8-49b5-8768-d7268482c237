# DEX Coordination for Pure Market Making Strategy

## Overview

The DEX Coordination feature enables the Pure Market Making strategy to monitor Uniswap V3 pools on Base network and automatically adjust order placement on MEXC to minimize arbitrage risks and optimize cross-platform trading.

## Key Features

### 1. Real-time DEX Price Monitoring
- Monitors FULA/WETH pool on Uniswap V3 Base network
- Updates DEX prices every 15 seconds via The Graph API
- Tracks pool liquidity and 24h volume
- Automatic failover to backup price sources

### 2. Arbitrage Protection
- Detects price deviations between MEXC and DEX
- Automatically widens spreads when arbitrage opportunities are detected
- Prevents systematic losses from cross-platform arbitrage

### 3. Dynamic Spread Adjustment
- Adjusts spreads based on DEX liquidity levels
- Increases spreads during low liquidity periods
- Proportional spread adjustments based on price deviation magnitude

### 4. Emergency Stop Protection
- Triggers emergency stop when price deviation exceeds threshold
- Significantly widens spreads to protect against extreme market conditions
- Automatic recovery when conditions normalize

## Configuration

### Basic Setup

To enable DEX coordination, set the following parameters in your configuration file:

```yaml
# Enable DEX coordination
enable_dex_coordination: true

# DEX configuration for Uniswap V3 on Base
dex_chain: "base"
dex_network: "mainnet"
dex_connector: "uniswap"

# Token addresses (REQUIRED)
dex_base_token_address: "0x[FULA_TOKEN_ADDRESS]"  # Replace with actual FULA token address
dex_quote_token_address: "******************************************"  # WETH on Base

# Pool configuration
dex_pool_fee_tier: 3000  # 0.3% fee tier
dex_pool_address: null   # Optional: specific pool address if known
```

### Advanced Configuration

```yaml
# Price monitoring settings
dex_price_update_interval: 15.0      # Update DEX price every 15 seconds
dex_price_staleness_threshold: 60.0  # Consider price stale after 60 seconds

# Arbitrage protection
enable_arbitrage_protection: true
max_price_deviation_pct: 2.0                    # 2% maximum deviation before protection
arbitrage_protection_spread_adjustment: 0.5     # Add 0.5% to spreads when arbitrage detected

# Liquidity-based adjustments
enable_dex_liquidity_adjustment: true
min_dex_liquidity_threshold: 10000.0           # $10,000 minimum liquidity threshold
low_liquidity_spread_multiplier: 1.5           # 1.5x spread multiplier for low liquidity

# Risk management
enable_cross_platform_risk_limits: true
max_daily_arbitrage_exposure: 5000.0           # $5,000 maximum daily arbitrage exposure
emergency_stop_price_deviation: 5.0            # 5% emergency stop threshold
```

### Order Coordination (Advanced)

```yaml
# Order coordination between MEXC and DEX (experimental)
enable_order_coordination: false        # Disabled by default
coordination_delay_seconds: 2.0         # 2-second delay between platform orders
max_coordination_attempts: 3            # Maximum coordination attempts
```

## How It Works

### 1. Price Monitoring
The system continuously monitors:
- MEXC order book prices
- Uniswap V3 pool prices via The Graph API
- Pool liquidity levels
- Trading volume

### 2. Arbitrage Detection
When price deviation between MEXC and DEX exceeds the threshold:
- System detects arbitrage opportunity
- Automatically increases spreads to protect against arbitrage
- Logs detailed information about the deviation

### 3. Spread Adjustments
The system applies multiple layers of spread adjustments:

1. **Base Spread**: Original bid/ask spreads from configuration
2. **Arbitrage Protection**: Additional spread when arbitrage detected
3. **Liquidity Adjustment**: Spread multiplier based on DEX liquidity
4. **Emergency Protection**: Significant spread increase for extreme deviations

### 4. Risk Management
- Monitors daily arbitrage exposure
- Triggers emergency stops for extreme price deviations
- Provides comprehensive logging and monitoring

## Example Configuration

Here's a complete example configuration for FULA token market making with DEX coordination:

```yaml
# Basic market making parameters
exchange: mexc
market: FULA-USDT
bid_spread: 0.5
ask_spread: 0.5
order_amount: 1000.0
order_levels: 2

# DEX Coordination
enable_dex_coordination: true
dex_base_token_address: "0x[FULA_TOKEN_ADDRESS_ON_BASE]"
dex_quote_token_address: "******************************************"
dex_pool_fee_tier: 3000

# Arbitrage protection
max_price_deviation_pct: 2.0
arbitrage_protection_spread_adjustment: 0.5

# Liquidity adjustments
min_dex_liquidity_threshold: 10000.0
low_liquidity_spread_multiplier: 1.5

# Emergency protection
emergency_stop_price_deviation: 5.0
```

## Monitoring and Logging

The system provides comprehensive logging:

### Regular Status Updates (Every 5 minutes)
```
🔗 DEX COORDINATION STATUS
💱 MEXC Price: $0.001234
🔗 DEX Price: $0.001245
📈 Price Deviation: 0.89%
💧 DEX Liquidity: $15,432.50
📊 DEX 24h Volume: $8,765.43
📏 Current Bid Spread: 0.75%
📏 Current Ask Spread: 0.75%
⚠️  Arbitrage Detected: false
🔻 Low Liquidity: false
🛑 Emergency Stop: false
```

### Event-Based Alerts
- Arbitrage opportunity detection
- Low liquidity warnings
- Emergency stop triggers
- Spread adjustment notifications

## Troubleshooting

### Common Issues

1. **DEX Price Not Updating**
   - Check internet connectivity
   - Verify token addresses are correct
   - Ensure The Graph API is accessible

2. **High Spread Adjustments**
   - Check if arbitrage protection is triggering frequently
   - Verify DEX liquidity levels
   - Review price deviation thresholds

3. **Emergency Stops**
   - Check for extreme market volatility
   - Verify price feeds are accurate
   - Review emergency stop thresholds

### Debug Mode
Enable detailed logging by setting log level to DEBUG in your Hummingbot configuration.

## Best Practices

1. **Start with Conservative Settings**
   - Begin with higher deviation thresholds
   - Use larger spread adjustments initially
   - Monitor performance before optimizing

2. **Monitor DEX Liquidity**
   - Ensure sufficient liquidity in the monitored pool
   - Adjust thresholds based on typical liquidity levels

3. **Regular Monitoring**
   - Review logs regularly for arbitrage events
   - Monitor spread adjustment frequency
   - Track overall strategy performance

4. **Risk Management**
   - Set appropriate emergency stop thresholds
   - Monitor daily arbitrage exposure
   - Have manual override procedures ready

## Dependencies

- `aiohttp` for HTTP requests
- `web3` for blockchain interactions (optional)
- The Graph API access for Uniswap data
- Stable internet connection

## Support

For issues or questions about DEX coordination:
1. Check the logs for detailed error messages
2. Verify configuration parameters
3. Test with small order sizes initially
4. Monitor system performance closely

## Future Enhancements

Planned improvements include:
- Support for multiple DEX protocols
- Advanced order coordination strategies
- Machine learning-based spread optimization
- Cross-chain price monitoring
