# DEX Wallet Setup Guide for Coordinated Price Support

## Overview

For the coordinated DEX + MEXC price support strategy to work with real transactions, you need to provide wallet credentials and sufficient funds. This guide explains how to set up the wallet configuration safely.

## ⚠️ SECURITY WARNING

**NEVER share your private key or seed phrase with anyone!**
- Store private keys securely (encrypted files, hardware wallets, etc.)
- Use a dedicated wallet for trading, not your main holdings
- Test with small amounts first
- Consider using a separate wallet just for this strategy

## Required Setup

### 1. **Wallet Requirements**

You need an Ethereum wallet with:
- **WETH (Wrapped Ethereum)** for swapping to FUAL on Uniswap
- **ETH** for gas fees (transaction costs)
- **Private key** access (not just seed phrase)

### 2. **Minimum Balances**

```
Recommended minimum balances:
- WETH: 0.5 ETH worth (~$1,000 at $2,000 ETH price)
- ETH: 0.05 ETH for gas fees (~$100)
- Total: ~$1,100 minimum for meaningful DEX support
```

### 3. **Configuration Parameters**

Add these parameters when creating the strategy:

```python
# DEX Wallet Configuration
dex_wallet_private_key="YOUR_64_CHARACTER_PRIVATE_KEY_HERE",  # 🔐 KEEP SECURE!
dex_wallet_address="0xYourWalletAddressHere",  # Your wallet address
web3_rpc_url="https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID",  # Ethereum RPC
dex_enabled=False,  # Set to True for real transactions (False = simulation)
min_weth_balance=Decimal("0.1"),  # Minimum WETH balance required
max_gas_price_gwei=Decimal("50"),  # Maximum gas price (50 Gwei)
slippage_tolerance_pct=Decimal("2.0")  # 2% slippage tolerance
```

## Step-by-Step Setup

### Step 1: Create/Use a Dedicated Wallet

**Option A: Create New Wallet (Recommended)**
```bash
# Using MetaMask, Trust Wallet, or any Ethereum wallet
# 1. Create new wallet
# 2. Save seed phrase securely
# 3. Export private key
# 4. Note down wallet address
```

**Option B: Use Existing Wallet**
```bash
# If using existing wallet:
# 1. Export private key from MetaMask/wallet
# 2. Ensure it has WETH and ETH
```

### Step 2: Get Private Key

**From MetaMask:**
1. Open MetaMask
2. Click account menu → Account Details
3. Click "Export Private Key"
4. Enter password
5. Copy the 64-character hex string (without 0x prefix)

**Format:** `1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef`

### Step 3: Get Infura/Alchemy RPC URL

**Option A: Infura (Free)**
1. Go to https://infura.io/
2. Create account and project
3. Get project ID
4. URL: `https://mainnet.infura.io/v3/YOUR_PROJECT_ID`

**Option B: Alchemy (Free)**
1. Go to https://alchemy.com/
2. Create account and app
3. Get API key
4. URL: `https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY`

### Step 4: Fund the Wallet

**Get WETH:**
```bash
# Method 1: Wrap ETH to WETH on Uniswap
# 1. Go to app.uniswap.org
# 2. Connect wallet
# 3. Swap ETH → WETH (1:1 ratio + gas)

# Method 2: Buy WETH directly on exchange
# 1. Buy on Coinbase/Binance
# 2. Withdraw to your wallet address
```

**Get ETH for Gas:**
```bash
# Ensure you have 0.05+ ETH for transaction fees
# Gas costs vary: 0.001-0.01 ETH per transaction
```

### Step 5: Configuration Example

```python
# Example strategy configuration
strategy = PriceSupportStrategy(
    market_info=market_info,
    target_price_pct=Decimal("10.0"),  # 10% price increase target
    max_budget=Decimal("2000"),  # $2000 total budget
    
    # DEX Configuration
    dex_wallet_private_key="abcd1234...",  # Your 64-char private key
    dex_wallet_address="******************************************",
    web3_rpc_url="https://mainnet.infura.io/v3/your-project-id",
    dex_enabled=False,  # Start with False for testing
    min_weth_balance=Decimal("0.1"),
    max_gas_price_gwei=Decimal("50"),
    slippage_tolerance_pct=Decimal("2.0")
)
```

## Testing Process

### Phase 1: Simulation Mode (Safe)
```python
dex_enabled=False  # Logs what it would do, no real transactions
```

### Phase 2: Small Test (Real but Limited)
```python
dex_enabled=True
max_budget=Decimal("50")  # Test with $50 first
```

### Phase 3: Full Production
```python
dex_enabled=True
max_budget=Decimal("2000")  # Full budget
```

## Current Implementation Status

### ✅ **Ready Now:**
- Wallet configuration parameters
- Validation of private key format
- Simulation mode with detailed logging
- Budget allocation and coordination logic

### 🔄 **Needs Web3 Integration:**
- Actual balance checking
- Real Uniswap swap execution
- Transaction monitoring
- Gas price optimization

### 📋 **Web3 Dependencies Needed:**
```bash
pip install web3
pip install eth-account
```

## Security Best Practices

### 1. **Environment Variables**
```bash
# Store sensitive data in environment variables
export DEX_PRIVATE_KEY="your_private_key_here"
export WEB3_RPC_URL="your_rpc_url_here"

# Use in Python:
import os
dex_wallet_private_key=os.getenv("DEX_PRIVATE_KEY")
```

### 2. **Encrypted Storage**
```python
# Consider encrypting private key
from cryptography.fernet import Fernet

# Encrypt private key before storing
key = Fernet.generate_key()
cipher = Fernet(key)
encrypted_pk = cipher.encrypt(private_key.encode())
```

### 3. **Separate Trading Wallet**
- Use dedicated wallet only for this strategy
- Don't store large amounts
- Regular monitoring and withdrawal of profits

## Monitoring and Safety

### Real-time Monitoring:
```
PRICE_SUPPORT: 🔄 Executing DEX price support with $400.00
PRICE_SUPPORT: 📊 Checking wallet balance for $400.00 swap
PRICE_SUPPORT: 🚀 Executing real DEX transaction...
PRICE_SUPPORT: ✅ DEX swap completed successfully
```

### Safety Features:
- Minimum balance checks before transactions
- Gas price limits to prevent overpaying
- Slippage protection
- Transaction validation
- Automatic fallback to MEXC-only if DEX fails

## Installation Requirements

### Install Web3 Dependencies

```bash
# Install required Python packages
pip install web3==6.11.3
pip install eth-account==0.9.0

# Verify installation
python -c "from web3 import Web3; print('Web3 installed successfully')"
python -c "from eth_account import Account; print('eth-account installed successfully')"
```

### Contract Addresses (Ethereum Mainnet)

The strategy uses these verified contract addresses:

```python
# Uniswap V3 Contracts
UNISWAP_V3_ROUTER = "******************************************"
UNISWAP_V3_QUOTER = "******************************************"

# Token Addresses
WETH_ADDRESS = "******************************************"
FUAL_ADDRESS = "******************************************"  # Your token
```

## Complete Configuration Example

```python
from decimal import Decimal
from hummingbot.strategy.price_support.price_support import PriceSupportStrategy

# Create strategy with full DEX integration
strategy = PriceSupportStrategy(
    market_info=market_info,
    target_price_pct=Decimal("10.0"),  # 10% price increase target
    max_budget=Decimal("2000"),        # $2000 total budget

    # Basic Settings
    stop_loss_pct=Decimal("5.0"),
    analysis_interval=30.0,
    coordination_enabled=True,

    # DEX Wallet Configuration
    dex_wallet_private_key="your_64_character_private_key_here",
    dex_wallet_address="******************************************",
    web3_rpc_url="https://mainnet.infura.io/v3/your-project-id",
    dex_enabled=False,  # Start with False for testing

    # DEX Trading Parameters
    min_weth_balance=Decimal("0.1"),        # Minimum WETH balance
    max_gas_price_gwei=Decimal("50"),       # Max gas price (50 Gwei)
    slippage_tolerance_pct=Decimal("2.0"),  # 2% slippage tolerance

    # Advanced Settings
    support_to_push_ratio=Decimal("4.0"),   # 4:1 support to push ratio
    support_layers=3,                       # Number of support layers
    stabilization_time=300.0,               # 5 minutes stabilization
    order_age_timeout=1800.0                # 30 minutes order timeout
)
```

## Real Transaction Flow

When `dex_enabled=True`, the strategy will:

### 1. **Initialization**
```
PRICE_SUPPORT: 🔄 Initializing Web3 connection...
PRICE_SUPPORT: ✅ Web3 initialized successfully
PRICE_SUPPORT: - Wallet: ******************************************
PRICE_SUPPORT: - Network: 1
```

### 2. **Balance Checking**
```
PRICE_SUPPORT: 📊 Checking wallet balance for $400.00 swap
PRICE_SUPPORT: - ETH Balance: 0.0523 ETH
PRICE_SUPPORT: - WETH Balance: 0.2156 WETH
PRICE_SUPPORT: - Required WETH: 0.2000 WETH
PRICE_SUPPORT: - Min ETH for gas: 0.0100 ETH
PRICE_SUPPORT: ✅ Sufficient balances for swap
```

### 3. **Swap Execution**
```
PRICE_SUPPORT: 🔄 Preparing Uniswap swap for $400.00
PRICE_SUPPORT: 📝 Swap Parameters:
PRICE_SUPPORT: - Input: 0.200000 WETH
PRICE_SUPPORT: - Expected Output: 1234567.890123 FUAL
PRICE_SUPPORT: - Min Output: 1209876.543210 FUAL
PRICE_SUPPORT: - Slippage: 2.0%
PRICE_SUPPORT: 🔄 Approving WETH spending...
PRICE_SUPPORT: ✅ WETH approval confirmed
PRICE_SUPPORT: 🚀 Executing real DEX transaction...
PRICE_SUPPORT: ⏳ Waiting for transaction confirmation: 0xabc123...
PRICE_SUPPORT: ✅ Transaction confirmed in block 18567890
PRICE_SUPPORT: ✅ Swap completed successfully! TX: 0xabc123...
```

## Testing Phases

### Phase 1: Simulation Testing
```python
dex_enabled=False  # Safe simulation mode
max_budget=Decimal("100")  # Small test budget
```

**Expected Output:**
```
PRICE_SUPPORT: 📝 DEX Support Action (Simulation Mode):
PRICE_SUPPORT: - Would swap $40.00 WETH for FUAL on Uniswap
PRICE_SUPPORT: - Estimated price impact: 1.23%
PRICE_SUPPORT: - Set dex_enabled=True to execute real transactions
```

### Phase 2: Small Real Transactions
```python
dex_enabled=True
max_budget=Decimal("50")  # Very small real test
```

### Phase 3: Production
```python
dex_enabled=True
max_budget=Decimal("2000")  # Full production budget
```

## Error Handling

The strategy includes comprehensive error handling:

### Common Errors and Solutions

**1. Web3 Connection Issues**
```
PRICE_SUPPORT: ❌ Failed to connect to Ethereum network
```
**Solution:** Check your RPC URL and internet connection

**2. Insufficient Balance**
```
PRICE_SUPPORT: ❌ Insufficient WETH: 0.0500 < 0.2000
```
**Solution:** Add more WETH to your wallet

**3. Gas Price Too High**
```
PRICE_SUPPORT: ❌ Gas price exceeds maximum: 75 > 50 Gwei
```
**Solution:** Increase `max_gas_price_gwei` or wait for lower gas

**4. Transaction Failed**
```
PRICE_SUPPORT: ❌ Transaction failed: 0xabc123...
```
**Solution:** Check Etherscan for failure reason, usually slippage or gas

## Security Features

### Built-in Protections:
- ✅ Private key validation (64 hex characters)
- ✅ Balance checks before transactions
- ✅ Gas price limits
- ✅ Slippage protection
- ✅ Transaction timeout (5 minutes)
- ✅ Approval amount optimization
- ✅ Network validation

### Monitoring:
- Real-time balance tracking
- Transaction confirmation monitoring
- Gas price optimization
- Slippage calculation
- Error logging and recovery

## Next Steps

1. **Install Web3 dependencies** (`pip install web3 eth-account`)
2. **Set up wallet and fund with WETH**
3. **Test in simulation mode first** (`dex_enabled=False`)
4. **Configure RPC provider** (Infura/Alchemy)
5. **Start with small amounts** for testing
6. **Scale up gradually** as confidence builds

## Production Checklist

- [ ] Web3 libraries installed
- [ ] Wallet funded with WETH and ETH
- [ ] RPC provider configured
- [ ] Private key securely stored
- [ ] Tested in simulation mode
- [ ] Small real transaction successful
- [ ] Monitoring and alerts set up
- [ ] Emergency stop procedures defined

The strategy is now fully equipped with Web3 integration for real Uniswap transactions, providing a complete coordinated DEX + MEXC price support solution!
