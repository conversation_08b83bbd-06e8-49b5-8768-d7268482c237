#!/bin/bash

# Setup SSL certificates for Hummingbot Gateway
echo "Setting up SSL certificates for Hummingbot Gateway..."

# Create certs directory
mkdir -p ~/gateway_certs

# Generate private key
openssl genrsa -out ~/gateway_certs/server_key.pem 2048

# Generate certificate signing request
openssl req -new -key ~/gateway_certs/server_key.pem -out ~/gateway_certs/server.csr -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Generate self-signed certificate
openssl x509 -req -in ~/gateway_certs/server.csr -signkey ~/gateway_certs/server_key.pem -out ~/gateway_certs/server_cert.pem -days 365

# Generate CA certificate (copy of server cert for self-signed)
cp ~/gateway_certs/server_cert.pem ~/gateway_certs/ca_cert.pem

# Generate client key and certificate
openssl genrsa -out ~/gateway_certs/client_key.pem 2048
openssl req -new -key ~/gateway_certs/client_key.pem -out ~/gateway_certs/client.csr -subj "/C=US/ST=State/L=City/O=Organization/CN=client"
openssl x509 -req -in ~/gateway_certs/client.csr -signkey ~/gateway_certs/client_key.pem -out ~/gateway_certs/client_cert.pem -days 365

# Set proper permissions
chmod 600 ~/gateway_certs/*.pem
chmod 644 ~/gateway_certs/*.csr

echo "SSL certificates generated in ~/gateway_certs/"
echo "Now run Gateway with volume mount:"
echo "docker run -p 15888:15888 -v ~/gateway_certs:/home/<USER>/certs hummingbot/gateway:latest"
