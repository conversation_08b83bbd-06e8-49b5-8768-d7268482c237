#!/bin/bash

set -e

HUMMINGBOT_DIR="/root/hummingbot"
CONFIG_PASSWORD="Gent#!#ski556"
CONFIG_FILE_NAME="conf_pure_mm_1.yml"
LOG_FILE="/var/log/hummingbot/hummingbot-service.log"
PID_FILE="/var/run/hummingbot.pid"

mkdir -p /var/log/hummingbot

echo "" > "$LOG_FILE"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - SERVICE: $1" | tee -a "$LOG_FILE"
}

cleanup() {
    log_message "Service stopping - cleaning up..."
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
    fi
}

trap cleanup EXIT

# Initialize conda
if [ -f "/root/miniconda/etc/profile.d/conda.sh" ]; then
    source "/root/miniconda/etc/profile.d/conda.sh"
    conda activate hummingbot
    log_message "Activated conda environment: $CONDA_DEFAULT_ENV"
else
    log_message "ERROR: Could not find conda installation"
    exit 1
fi

cd "$HUMMINGBOT_DIR"

log_message "Starting Hummingbot built-in pure market making strategy..."
log_message "Config file: $CONFIG_FILE_NAME"

if [ ! -f "conf/strategies/$CONFIG_FILE_NAME" ]; then
    log_message "ERROR: Configuration file not found: conf/strategies/$CONFIG_FILE_NAME"
    exit 1
fi

echo $$ > "$PID_FILE"

# Set environment variables to handle non-terminal execution
export PYTHONUNBUFFERED=1
export TERM=xterm-256color

log_message "Starting Hummingbot with environment setup for non-terminal execution..."

# Use script command to provide a pseudo-terminal
script -qec "python bin/hummingbot_quickstart.py -p '$CONFIG_PASSWORD' -f '$CONFIG_FILE_NAME'" /dev/null 2>&1 | tee -a "$LOG_FILE"

EXIT_CODE=$?
log_message "Hummingbot exited with code: $EXIT_CODE"

if [ $EXIT_CODE -eq 0 ]; then
    log_message "Hummingbot service stopped normally"
else
    log_message "Hummingbot service stopped with error code: $EXIT_CODE"
fi
