#!/bin/bash

PID_FILE="/var/run/hummingbot.pid"
LOG_FILE="/var/log/hummingbot/hummingbot-service.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - SHUTDOWN: $1" | tee -a "$LOG_FILE"
}

# Method 1: Try MQTT Stop with complete RPC message format
try_mqtt_stop() {
    local pid=$1
    log_message "Attempting stop via MQTT..."
    
    if command -v mosquitto_pub >/dev/null 2>&1; then
        # Get correct instance ID and namespace from config
        local instance_id=$(grep "instance_id:" /root/hummingbot/conf/conf_client.yml | awk '{print $2}' | tr -d '"')
        local namespace=$(grep -A 10 "mqtt_bridge:" /root/hummingbot/conf/conf_client.yml | grep "mqtt_namespace:" | awk '{print $2}' | tr -d '"')
        
        if [ -n "$instance_id" ] && [ -n "$namespace" ]; then
            local topic="$namespace/$instance_id/stop"
            local message='{"header": {"reply_to": "shutdown/reply"}, "data": {"skip_order_cancellation": false, "async_backend": true}}'
            
            log_message "Sending MQTT stop to topic: $topic"
            log_message "Message: $message"
            mosquitto_pub -h localhost -t "$topic" -m "$message" 2>/dev/null
            
            log_message "MQTT stop command sent"
            return 0
        else
            log_message "Could not get instance_id or namespace from config"
            return 1
        fi
    else
        log_message "mosquitto_pub not available"
        return 1
    fi
}

# Method 2: Try Management Console
try_management_console_stop() {
    local pid=$1
    log_message "Attempting stop via management console..."
    
    if netstat -tln | grep -q ":8211"; then
        log_message "Debug console detected on port 8211, sending stop command..."
        
        timeout 10 ssh -o StrictHostKeyChecking=no -o ConnectTimeout=3 -p 8211 user@localhost << 'SSH_EOF' 2>/dev/null && {
            try:
                if 'hb' in locals():
                    hb.stop()
                    print("Stop command sent successfully")
                else:
                    print("Hummingbot instance not found in console")
            except Exception as e:
                print(f"Error sending stop command: {e}")
            exit()
SSH_EOF
        
        log_message "Management console stop command sent"
        return 0
    else
        log_message "Debug console not available on port 8211"
        return 1
    fi
}

# Method 3: Signal-based stop
try_signal_stop() {
    local pid=$1
    log_message "Attempting graceful stop via signals..."
    
    # Send SIGTERM
    kill -TERM "$pid" 2>/dev/null || return 1
    
    # Wait up to 30 seconds
    for i in {1..30}; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log_message "Process $pid terminated gracefully after ${i} seconds"
            return 0
        fi
        sleep 1
        if [ $((i % 10)) -eq 0 ]; then
            log_message "Still waiting for graceful shutdown... (${i}/30 seconds)"
        fi
    done
    
    return 1
}

# Main shutdown logic
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    log_message "Initiating graceful shutdown of Hummingbot (PID: $PID)..."
    
    if kill -0 "$PID" 2>/dev/null; then
        log_message "Process $PID is running, trying multiple shutdown methods..."
        
        # Try Method 1: MQTT (most reliable for Hummingbot)
        if try_mqtt_stop "$PID"; then
            # Wait for the command to take effect
            for i in {1..15}; do
                if ! kill -0 "$PID" 2>/dev/null; then
                    log_message "Successfully stopped via MQTT after ${i} seconds"
                    rm -f "$PID_FILE"
                    exit 0
                fi
                sleep 1
            done
        fi
        
        # Try Method 2: Management Console
        if try_management_console_stop "$PID"; then
            # Wait for the command to take effect
            for i in {1..10}; do
                if ! kill -0 "$PID" 2>/dev/null; then
                    log_message "Successfully stopped via management console after ${i} seconds"
                    rm -f "$PID_FILE"
                    exit 0
                fi
                sleep 1
            done
        fi
        
        # Try Method 3: Signals
        if try_signal_stop "$PID"; then
            log_message "Successfully stopped via signals"
            rm -f "$PID_FILE"
            exit 0
        fi
        
        # Method 4: SIGINT as backup
        log_message "All graceful methods failed, sending SIGINT..."
        kill -INT "$PID" 2>/dev/null || true
        sleep 10
        
        # Method 5: Force kill as last resort
        if kill -0 "$PID" 2>/dev/null; then
            log_message "Process still running, forcing shutdown with SIGKILL..."
            kill -KILL "$PID" 2>/dev/null || true
        fi
        
        rm -f "$PID_FILE"
        log_message "Shutdown process completed"
        
    else
        log_message "Process $PID not found, cleaning up PID file"
        rm -f "$PID_FILE"
    fi
else
    log_message "No PID file found, Hummingbot may not be running"
fi

log_message "Graceful shutdown script completed"
exit 0
